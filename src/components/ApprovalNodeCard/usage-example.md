# ApprovalNodeCard 使用示例

## 在审批页面中使用

```tsx
import React from 'react';
import { Space } from 'antd';
import ApprovalNodeCard, { NodeStatus } from '@/components/ApprovalNodeCard';

const ApprovalPage: React.FC = () => {
  // 模拟审批流程数据
  const approvalNodes = [
    {
      id: '1',
      nodeName: '发起申请',
      status: 'completed' as NodeStatus,
      person: '张三',
      date: '2024-01-15 09:30',
      content: '申请出差报销，金额：2000元',
    },
    {
      id: '2',
      nodeName: '部门经理审批',
      status: 'completed' as NodeStatus,
      person: '李四',
      date: '2024-01-15 14:20',
      content: '同意申请，符合公司政策',
    },
    {
      id: '3',
      nodeName: '财务审核',
      status: 'approving' as NodeStatus,
      person: '王五',
      date: '2024-01-16 10:15',
      content: '正在审核发票和凭证',
    },
    {
      id: '4',
      nodeName: '总经理审批',
      status: 'initiate' as NodeStatus,
      person: '赵六',
      date: '',
      content: '等待审批',
    },
  ];

  const handleNodeClick = (nodeId: string, nodeName: string) => {
    console.log(`点击了节点: ${nodeId} - ${nodeName}`);
    // 这里可以添加查看详情、编辑等逻辑
  };

  return (
    <div style={{ padding: '24px' }}>
      <h2>审批流程</h2>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {approvalNodes.map((node) => (
          <ApprovalNodeCard
            key={node.id}
            nodeName={node.nodeName}
            status={node.status}
            person={node.person}
            date={node.date}
            content={node.content}
            onClick={() => handleNodeClick(node.id, node.nodeName)}
          />
        ))}
      </Space>
    </div>
  );
};

export default ApprovalPage;
```

## 在审批详情页面中使用

```tsx
import React from 'react';
import { Card, Space } from 'antd';
import ApprovalNodeCard from '@/components/ApprovalNodeCard';

interface ApprovalDetailProps {
  approvalId: string;
}

const ApprovalDetail: React.FC<ApprovalDetailProps> = ({ approvalId }) => {
  // 从API获取审批详情
  const approvalDetail = {
    title: '出差报销申请',
    applicant: '张三',
    amount: 2000,
    nodes: [
      {
        nodeName: '申请提交',
        status: 'completed' as const,
        person: '张三',
        date: '2024-01-15 09:30',
        content: '提交出差报销申请',
      },
      {
        nodeName: '部门审批',
        status: 'completed' as const,
        person: '李四',
        date: '2024-01-15 14:20',
        content: '审批通过',
      },
      {
        nodeName: '财务审核',
        status: 'approving' as const,
        person: '王五',
        date: '2024-01-16 10:15',
        content: '审核中...',
      },
    ],
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title={approvalDetail.title} style={{ marginBottom: '24px' }}>
        <p>申请人：{approvalDetail.applicant}</p>
        <p>金额：¥{approvalDetail.amount}</p>
      </Card>

      <Card title="审批流程">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {approvalDetail.nodes.map((node, index) => (
            <ApprovalNodeCard
              key={index}
              nodeName={node.nodeName}
              status={node.status}
              person={node.person}
              date={node.date}
              content={node.content}
            />
          ))}
        </Space>
      </Card>
    </div>
  );
};

export default ApprovalDetail;
```

## 自定义样式

```tsx
import React from 'react';
import ApprovalNodeCard from '@/components/ApprovalNodeCard';
import './custom-approval.less';

const CustomApprovalCard: React.FC = () => {
  return (
    <ApprovalNodeCard
      nodeName="特殊审批"
      status="completed"
      person="管理员"
      date="2024-01-16 16:45"
      content="特殊流程审批完成"
      className="custom-approval-card"
    />
  );
};
```

对应的样式文件 `custom-approval.less`：

```less
.custom-approval-card {
  border: 2px solid #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.25);
  }
}
```
