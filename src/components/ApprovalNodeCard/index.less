.approval-node-card {
  position: relative;
  display: flex;
  width: 75%;
  max-width: 384px;
  min-height: 96px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }

  .wave-decoration {
    display: flex;
    flex-shrink: 0;
    align-items: stretch;
    width: 16px;
    height: 100%;
    min-height: 96px;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  .wave-path {
    stroke-linecap: round;
    stroke-width: 2;
  }

  .content-area {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    padding: 6px 10px;
  }

  .node-title {
    margin: 6px 12px 0 0;
    overflow: hidden;
    color: #000;
    font-weight: bold;
    font-size: 20px;
    line-height: 32px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .node-details {
    margin: 0;
    padding: 0;
    color: #9ca3af;
    font-size: 14px;
    line-height: 20px;
    word-break: break-all;

    div {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .right-icon-slot {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 64px;
  }

  // 不同状态的颜色配置
  &.status-initiate {
    .wave-path {
      fill: #1890ff;
      stroke: #1890ff;
    }
    .node-title {
      color: #1890ff;
    }
  }

  &.status-approving {
    .wave-path {
      fill: #faad14;
      stroke: #faad14;
    }
    .node-title {
      color: #faad14;
    }
  }

  &.status-cc {
    .wave-path {
      fill: #722ed1;
      stroke: #722ed1;
    }
    .node-title {
      color: #722ed1;
    }
  }

  &.status-completed {
    .wave-path {
      fill: #66cdaa;
      stroke: #66cdaa;
    }
    .node-title {
      color: #66cdaa;
    }
  }
}
