.approval-node-card {
  position: relative;
  display: flex;
  width: 75%;
  max-width: 384px;
  height: 96px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }

  .wave-decoration {
    flex-shrink: 0;
    width: 16px;
    height: 96px;
  }

  .wave-path {
    stroke-linecap: round;
    stroke-width: 2;
  }

  .content-area {
    flex: 1;
    padding: 0 10px;
    overflow: hidden;
    width: 100%;
  }

  .node-title {
    margin: 6px 12px 0 0;
    color: #000;
    font-weight: bold;
    font-size: 20px;
    line-height: 32px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .node-details {
    margin: 0;
    padding: 0;
    color: #9ca3af;
    font-size: 14px;
    line-height: 20px;
    overflow: hidden;
    word-break: break-all;
    max-height: 40px;
  }

  .confirm-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    background: transparent;
    border: none;
    cursor: pointer;
    outline: none;

    &:focus {
      outline: none;
    }
  }

  .confirm-icon {
    width: 28px;
    height: 28px;
  }

  // 不同状态的颜色配置
  &.status-initiate {
    .wave-path {
      fill: #1890ff;
      stroke: #1890ff;
    }
    .node-title {
      color: #1890ff;
    }
    .confirm-icon {
      color: #1890ff;
    }
  }

  &.status-approving {
    .wave-path {
      fill: #faad14;
      stroke: #faad14;
    }
    .node-title {
      color: #faad14;
    }
    .confirm-icon {
      color: #faad14;
    }
  }

  &.status-cc {
    .wave-path {
      fill: #722ed1;
      stroke: #722ed1;
    }
    .node-title {
      color: #722ed1;
    }
    .confirm-icon {
      color: #722ed1;
    }
  }

  &.status-completed {
    .wave-path {
      fill: #66cdaa;
      stroke: #66cdaa;
    }
    .node-title {
      color: #66cdaa;
    }
    .confirm-icon {
      color: #66cdaa;
    }
  }
}
