import React from 'react';
import styles from './index.less';

// 节点状态类型
export type NodeStatus = 'initiate' | 'approving' | 'cc' | 'completed';

// 组件Props类型
export interface ApprovalNodeCardProps {
  /** 节点名称 */
  nodeName: string;
  /** 节点状态 */
  status: NodeStatus;
  /** 节点人员 */
  person: string;
  /** 节点日期 */
  date: string;
  /** 申请内容 */
  content: string;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: () => void;
}

const ApprovalNodeCard: React.FC<ApprovalNodeCardProps> = ({
  nodeName,
  status,
  person,
  date,
  content,
  className,
  onClick,
}) => {
  return (
    <div
      className={`${styles['approval-node-card']} ${styles[`status-${status}`]} ${className || ''}`}
      onClick={onClick}
    >
      {/* 左侧波浪形装饰 */}
      <svg
        width="16"
        xmlns="http://www.w3.org/2000/svg"
        className={styles['wave-decoration']}
        preserveAspectRatio="none"
        viewBox="0 0 16 96"
      >
        <path
          d="M 8 0
               Q 4 4.8, 8 9.6
               T 8 19.2
               Q 4 24, 8 28.8
               T 8 38.4
               Q 4 43.2, 8 48
               T 8 57.6
               Q 4 62.4, 8 67.2
               T 8 76.8
               Q 4 81.6, 8 86.4
               T 8 96
               L 0 96
               L 0 0
               Z"
          className={styles['wave-path']}
        />
      </svg>

      {/* 主要内容区域 */}
      <div className={styles['content-area']}>
        <p className={styles['node-title']}>{nodeName}</p>
        <div className={styles['node-details']}>
          <div>{person}</div>
          <div>{date}</div>
          {content && <div>{content}</div>}
        </div>
      </div>

      {/* 右侧确认按钮 */}
      <button type="button" className={styles['confirm-button']}>
        <svg
          className={styles['confirm-icon']}
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
        </svg>
      </button>
    </div>
  );
};

export default ApprovalNodeCard;
