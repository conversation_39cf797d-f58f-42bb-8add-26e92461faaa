import React from 'react';
import styles from './index.less';

// 节点状态类型
export type NodeStatus = 'initiate' | 'approving' | 'cc' | 'completed';

// 组件Props类型
export interface ApprovalNodeCardProps {
  /** 节点名称 */
  nodeName: string;
  /** 节点状态 */
  status: NodeStatus;
  /** 节点人员 */
  person: string;
  /** 节点日期 */
  date: string;
  /** 申请内容 */
  content: string;
  /** 右侧logo插槽 */
  rightIcon?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: () => void;
}

const ApprovalNodeCard: React.FC<ApprovalNodeCardProps> = ({
  nodeName,
  status,
  person,
  date,
  content,
  rightIcon,
  className,
  onClick,
}) => {
  return (
    <div
      className={`${styles['approval-node-card']} ${styles[`status-${status}`]} ${className || ''}`}
      onClick={onClick}
    >
      {/* 左侧波浪形装饰 */}
      <svg
        className={styles['wave-decoration']}
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
        viewBox="0 0 16 100"
      >
        <path
          d="M 8 0
               Q 4 5, 8 10
               T 8 20
               Q 4 25, 8 30
               T 8 40
               Q 4 45, 8 50
               T 8 60
               Q 4 65, 8 70
               T 8 80
               Q 4 85, 8 90
               T 8 100
               L 0 100
               L 0 0
               Z"
          className={styles['wave-path']}
        />
      </svg>

      {/* 主要内容区域 */}
      <div className={styles['content-area']}>
        <p className={styles['node-title']}>{nodeName}</p>
        <div className={styles['node-details']}>
          <div>{person}</div>
          <div>{date}</div>
          {content && <div>{content}</div>}
        </div>
      </div>

      {/* 右侧图标插槽 */}
      {rightIcon && <div className={styles['right-icon-slot']}>{rightIcon}</div>}
    </div>
  );
};

export default ApprovalNodeCard;
