# ApprovalNodeCard 审批流程节点卡片组件

一个用于显示审批流程节点信息的卡片组件，支持不同状态的颜色主题。

## 功能特性

- 🎨 支持四种不同状态的颜色主题（发起、审批中、抄送、结束）
- 📱 响应式设计，适配不同屏幕尺寸
- 🎯 可点击交互，支持自定义点击事件
- 🎪 优雅的波浪形装饰和悬停效果
- 📝 完整的 TypeScript 类型支持

## 组件参数

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| nodeName | 节点名称 | string | - | ✅ |
| status | 节点状态 | 'initiate' \| 'approving' \| 'cc' \| 'completed' | - | ✅ |
| person | 节点人员 | string | - | ✅ |
| date | 节点日期 | string | - | ✅ |
| content | 申请内容 | string | - | ✅ |
| className | 自定义类名 | string | - | ❌ |
| onClick | 点击事件 | () => void | - | ❌ |

## 状态说明

| 状态值 | 说明 | 颜色 |
|--------|------|------|
| initiate | 发起 | 蓝色 (#1890ff) |
| approving | 审批中 | 橙色 (#faad14) |
| cc | 抄送 | 紫色 (#722ed1) |
| completed | 结束 | 绿色 (#66cdaa) |

## 使用示例

### 基础用法

```tsx
import ApprovalNodeCard from '@/components/ApprovalNodeCard';

const Example = () => {
  return (
    <ApprovalNodeCard
      nodeName="部门审批"
      status="approving"
      person="张三"
      date="2024-01-15 14:20"
      content="审批中..."
    />
  );
};
```

### 带点击事件

```tsx
import ApprovalNodeCard from '@/components/ApprovalNodeCard';

const Example = () => {
  const handleClick = () => {
    console.log('节点被点击了');
  };

  return (
    <ApprovalNodeCard
      nodeName="审批完成"
      status="completed"
      person="李四"
      date="2024-01-16 16:45"
      content="审批通过，流程结束"
      onClick={handleClick}
    />
  );
};
```

### 批量渲染

```tsx
import { Space } from 'antd';
import ApprovalNodeCard, { NodeStatus } from '@/components/ApprovalNodeCard';

const Example = () => {
  const nodes = [
    {
      nodeName: '发起申请',
      status: 'initiate' as NodeStatus,
      person: '张三',
      date: '2024-01-15 09:30',
      content: '申请出差报销',
    },
    {
      nodeName: '部门审批',
      status: 'approving' as NodeStatus,
      person: '李四',
      date: '2024-01-15 14:20',
      content: '审批中...',
    },
  ];

  return (
    <Space direction="vertical" size="large">
      {nodes.map((node, index) => (
        <ApprovalNodeCard
          key={index}
          {...node}
        />
      ))}
    </Space>
  );
};
```

## 样式定制

组件使用 Less 编写样式，支持通过 CSS 变量或覆盖样式类进行定制：

```less
.approval-node-card {
  // 自定义样式
  &.custom-style {
    border: 2px solid #1890ff;
  }
}
```

## 注意事项

1. 组件宽度为父容器的 75%，最大宽度为 384px
2. 高度固定为 96px
3. 内容过长时会自动截断并显示省略号
4. 支持键盘导航和无障碍访问
