import { Space } from 'antd';
import React from 'react';
import ApprovalNodeCard, { NodeStatus } from './index';

/**
 * 审批节点卡片组件使用示例
 */
const ApprovalNodeCardDemo: React.FC = () => {
  // 示例数据
  const nodeData = [
    {
      nodeName: '发起申请',
      status: 'initiate' as NodeStatus,
      person: '张三',
      date: '2024-01-15 09:30',
      content: '申请出差报销，本次出差目的地为北京，预计出差时间3天，主要参加技术交流会议',
    },
    {
      nodeName: '部门审批',
      status: 'approving' as NodeStatus,
      person: '李四',
      date: '2024-01-15 14:20',
      content: '正在审批中，需要核实出差的必要性和预算合理性，预计今日下午完成审批',
    },
    {
      nodeName: '财务抄送',
      status: 'cc' as NodeStatus,
      person: '王五',
      date: '2024-01-16 10:15',
      content: '已抄送财务部门，财务部门将根据公司差旅标准进行费用核算和预算审核',
    },
    {
      nodeName: '审批完成',
      status: 'completed' as NodeStatus,
      person: '赵六',
      date: '2024-01-16 16:45',
      content: '审批通过，流程结束。申请人可以按照批准的出差计划执行，注意保留相关票据用于后续报销',
    },
    {
      nodeName: '短内容测试',
      status: 'initiate' as NodeStatus,
      person: '测试用户',
      date: '2024-01-17 09:00',
      content: '短内容',
    },
  ];

  const handleCardClick = (nodeName: string) => {
    console.log(`点击了节点: ${nodeName}`);
  };

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <h2 style={{ marginBottom: '24px', color: '#333' }}>审批流程节点卡片组件示例</h2>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {nodeData.map((node, index) => (
          <ApprovalNodeCard
            key={index}
            nodeName={node.nodeName}
            status={node.status}
            person={node.person}
            date={node.date}
            content={node.content}
            onClick={() => handleCardClick(node.nodeName)}
          />
        ))}
      </Space>

      <div
        style={{ marginTop: '48px', padding: '24px', backgroundColor: '#fff', borderRadius: '8px' }}
      >
        <h3>组件使用说明</h3>
        <ul>
          <li>
            <strong>nodeName</strong>: 节点名称
          </li>
          <li>
            <strong>status</strong>: 节点状态，支持四种状态：
            <ul>
              <li>&apos;initiate&apos; - 发起（蓝色）</li>
              <li>&apos;approving&apos; - 审批中（橙色）</li>
              <li>&apos;cc&apos; - 抄送（紫色）</li>
              <li>&apos;completed&apos; - 结束（绿色）</li>
            </ul>
          </li>
          <li>
            <strong>person</strong>: 节点人员
          </li>
          <li>
            <strong>date</strong>: 节点日期
          </li>
          <li>
            <strong>content</strong>: 申请内容
          </li>
          <li>
            <strong>onClick</strong>: 点击事件（可选）
          </li>
          <li>
            <strong>className</strong>: 自定义类名（可选）
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ApprovalNodeCardDemo;
