# ApprovalNodeCard 更新日志

## v1.0.0 (2024-01-16)

### ✨ 新功能
- 🎉 初始版本发布
- 📱 支持四种审批状态：发起、审批中、抄送、结束
- 🎨 每种状态对应不同的颜色主题
- 🖱️ 支持点击交互事件
- 📝 完整的 TypeScript 类型定义
- 🎪 优雅的波浪形装饰和悬停效果
- 📱 响应式设计，适配不同屏幕尺寸

### 🎨 设计特性
- 基于提供的 HTML + TailwindCSS 模板设计
- 使用项目统一的 Less 样式规范
- 遵循项目现有的组件开发模式
- 支持自定义样式扩展

### 📦 文件结构
```
src/components/ApprovalNodeCard/
├── index.tsx           # 主组件文件
├── index.less          # 样式文件
├── demo.tsx           # 使用示例
├── README.md          # 组件文档
├── usage-example.md   # 详细使用示例
└── CHANGELOG.md       # 更新日志
```

### 🔧 技术实现
- 使用 React + TypeScript 开发
- 采用 Less 编写样式，支持主题定制
- 遵循项目的 ESLint 和 TypeScript 规范
- 支持无障碍访问

### 📋 组件参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| nodeName | string | ✅ | 节点名称 |
| status | NodeStatus | ✅ | 节点状态 |
| person | string | ✅ | 节点人员 |
| date | string | ✅ | 节点日期 |
| content | string | ✅ | 申请内容 |
| className | string | ❌ | 自定义类名 |
| onClick | () => void | ❌ | 点击事件 |

### 🎯 状态类型
- `initiate`: 发起状态（蓝色 #1890ff）
- `approving`: 审批中状态（橙色 #faad14）
- `cc`: 抄送状态（紫色 #722ed1）
- `completed`: 完成状态（绿色 #66cdaa）

### 🚀 使用方法
```tsx
import ApprovalNodeCard from '@/components/ApprovalNodeCard';

<ApprovalNodeCard
  nodeName="部门审批"
  status="approving"
  person="张三"
  date="2024-01-15 14:20"
  content="审批中..."
/>
```

### 📝 注意事项
- 组件宽度为父容器的 75%，最大宽度 384px
- 高度固定为 96px
- 内容过长时自动截断显示省略号
- 支持键盘导航和无障碍访问
