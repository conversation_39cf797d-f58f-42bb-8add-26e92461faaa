declare namespace API {
  type abandonedInvoicingParams = {
    idReq: IdReq;
  };

  type ActiviInfoMobileResp = {
    hasUpdate?: boolean;
    activiStatus?: string;
    fromData?: Record<string, any>;
    currentAudiUsers?: string[];
    audiRecordList?: AudiRecordDTO[];
    processInstancesRank?: ProcessInstancesRankResp[];
    processType?: Record<string, any>;
  };

  type ActiviInfoResp = {
    hasUpdate?: boolean;
    activiStatus?: string;
    fromData?: Record<string, any>;
    currentAudiUsers?: string[];
    audiRecordList?: AudiRecordDTO[];
  };

  type addFileParams = {
    req: FileAddReq;
  };

  type addressBookParams = {
    searchUsername: string;
  };

  type addWechatFileParams = {
    req: FileAddReq;
  };

  type AdjustmentRecordResp = {
    /** 主键 */
    id?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 调整日期 */
    adjustmentDate?: string;
    /** 变更原因 */
    changeReason?: string;
    /** 基本工资 */
    baseSalary?: string;
    /** 其他工资 */
    otherSalary?: string;
    /** 税前工资 */
    preTaxSalary?: string;
    /** 调整比例 */
    adjustmentRatio?: number;
    /** 福利等级 */
    benefitLevel?: string;
    /** 备注 */
    remark?: string;
    /** 状态（默认 NORMAL */
    status?: string;
    /** AES密钥 */
    aesKey?: string;
  };

  type AgreeWorkFlowReq = {
    processInstanceId?: string;
    comments?: string;
  };

  type AnalysisInfoReq = {
    date?: Record<string, any>;
    dimension?: string;
    excludeIds?: string[];
  };

  type AnalysisPageReq = {
    date?: Record<string, any>;
    keyword?: string;
    pageNum?: number;
    pageSize?: number;
  };

  type AnnouncementInsertReq = {
    /** 标题 */
    title?: string;
    /** 描述 */
    content?: string;
  };

  type AnnouncementPageResp = {
    /** ID */
    id?: string;
    /** 标题 */
    title?: string;
    /** 内容 */
    content?: string;
    /** 发布时间 */
    releasedTime?: string;
    /** 是否new */
    hasNew?: boolean;
    /** 创建人 */
    createdBy?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type AnnouncementUpdateReq = {
    /** 公告id */
    id?: string;
    /** 标题 */
    title?: string;
    /** 描述 */
    content?: string;
  };

  type ApprovalHistoricResp = {
    id?: string;
    documentNumber?: Record<string, any>;
    authUsers?: string[];
    sponsor?: Record<string, any>;
    initiationTime?: string;
    type?: Record<string, any>;
    activiStatus?: Record<string, any>;
  };

  type ApprovalResp = {
    count?: number;
    historicProcessInstanceRespList?: ApprovalHistoricResp[];
  };

  type AptitudeReq = {
    /** 主键 */
    id?: string;
    /** 资质名称 */
    aptitudeName?: string;
    /** 资质类型 */
    aptitudeType?: string;
    /** 资质类别 */
    aptitudeCategory?: string;
    /** 认证时间 */
    authTime?: string;
    /** 过期时间 */
    expireTime?: string;
    /** 续费时间 */
    renewalTime?: string;
    /** 续费金额 */
    renewalAmount?: number;
    /** 负责人 */
    liabilityPerson?: string;
    /** 负责人主键 */
    liabilityPersonId?: string;
    /** 补充字段 */
    supplement?: Record<string, any>[];
    /** 资质证书编号 */
    certificateNumber?: string;
  };

  type AptitudeResp = {
    /** 主键 */
    id?: string;
    /** 资质编号 */
    aptitudeNumber?: string;
    /** 资质名称 */
    aptitudeName?: string;
    /** 资质类型 */
    aptitudeType?: string;
    /** 资质类别 */
    aptitudeCategory?: string;
    /** 认证时间 */
    authTime?: string;
    /** 过期时间 */
    expireTime?: string;
    /** 续费时间 */
    renewalTime?: string;
    /** 续费金额 */
    renewalAmount?: number;
    /** 负责人 */
    liabilityPerson?: string;
    /** 负责人主键 */
    liabilityPersonId?: string;
    /** 补充字段 */
    supplement?: Record<string, any>[];
    /** 资质证书编号 */
    certificateNumber?: string;
  };

  type AttAdmission = {
    id?: string;
    username?: string;
    gender?: string;
    personId?: string;
    phone?: string;
    bank?: string;
    bankAcNo?: string;
    dateOfJoining?: string;
    simulationImportantTime?: string;
    probationSalary?: string;
    changeJustSalary?: string;
    allowance?: string;
    createdTime?: string;
  };

  type AttChangeJust = {
    id?: string;
    username?: string;
    gender?: string;
    personId?: string;
    phone?: string;
    importantTime?: string;
    changeJustSalary?: string;
    allowance?: string;
    createdTime?: string;
  };

  type AttDimission = {
    id?: string;
    username?: string;
    gender?: string;
    personId?: string;
    phone?: string;
    relievingDate?: string;
    salaryEndDate?: string;
    createdTime?: string;
  };

  type attendanceCalendar1Params = {
    req: WeekExportReq;
  };

  type attendanceCalendar2Params = {
    req: AttListReq;
  };

  type AttendanceMonthResp = {
    /** 入职 */
    attAdmissionList?: AttAdmission[];
    /** 转正 */
    attChangeJustList?: AttChangeJust[];
    /** 离职 */
    attDimissionList?: AttDimission[];
    /** 休假 */
    attLeaveList?: AttLeave[];
  };

  type AttLeave = {
    id?: string;
    username?: string;
    gender?: string;
    leaveTypeName?: string;
    fromDate?: string;
    toDate?: string;
    actualLeaveDays?: string;
    createdTime?: string;
  };

  type AttListReq = {
    month?: string;
  };

  type AttRecordListResp = {
    /** ID */
    id?: string;
    /** 记录日期 */
    recordData?: string;
  };

  type AudiRecordDTO = {
    title?: string;
    userName?: string[];
    status?: string;
    audiTime?: string;
    comments?: string;
  };

  type AuthReq = {
    /** 手机号/邮箱 */
    account: string;
    /** 密码 */
    password: string;
  };

  type AuthResp = {
    /** 授权码 token */
    authorization?: string;
    /** ref token */
    refreshAuthorization?: string;
  };

  type AwaitPaymentPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortEstimatePayTime?: string;
    sortPayAmount?: string;
    sortPayTime?: string;
  };

  type BanksDto = {
    id?: string;
    /** 开票银行别名 */
    bankAs?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账号 */
    account?: string;
    /** 联系电话 */
    contactNumber?: string;
    /** 注册地址 */
    registeredAddress?: string;
    /** 是否默认 */
    hasDefault?: boolean;
  };

  type BillingInformationDTO = {
    /** 更新日期 */
    updateDate?: string;
    /** 公司邮寄地址 */
    mailingAddress?: string;
    /** 发票抬头 */
    invoiceTitle?: string;
    /** 纳税人识别号 */
    taxpayerId?: string;
    /** 注册地址 */
    registeredAddress?: string;
    /** 电话 */
    phoneNumber?: string;
    /** 开户名 */
    accountName?: string;
    /** 开户行 */
    bankName?: string;
    /** 账号 */
    bankAccount?: string;
  };

  type BonusListReq = {
    /** 贡献度 */
    contributionDegree?: number;
    /** 分配金额 */
    bonusAmount?: number;
  };

  type BrieflyProInfo = {
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 合同主键 */
    contractId?: string;
    /** 项目分类 */
    projectClassify?: string;
    /** 项目经理 */
    projectManger?: string;
    /** 项目创建时间 */
    proCreateTime?: string;
    /** 项目状态 */
    status?: string;
    /** 项目开销 */
    spend?: number;
    /** 已执行工作量 */
    executedWorkload?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type BrieflyProReq = {
    /** 客户id */
    ids?: string[];
    /** 项目分类 */
    projectClassify?: string;
  };

  type BusiInfoReq = {
    /** 工单ID */
    workId?: string;
    /** 对应工单 */
    workNumber?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 项目号 */
    projectNumber?: string;
    /** 日期 */
    time?: string;
    /** 发票 */
    invoice?: string;
    /** 费用类型 */
    type?: string;
    /** 金额 */
    money?: string;
    /** 实际报销类型 */
    actualType?: string;
    /** 实际报销金额 */
    actualMoney?: string;
    /** 正当理由 */
    reason?: string;
    /** 备注 */
    remark?: string;
    /** 报销类型 */
    finaType?: string;
  };

  type BusiInfoResp = {
    /** 工单ID */
    workId?: string;
    /** 对应工单 */
    workNumber?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 项目号 */
    projectNumber?: string;
    /** 日期 */
    time?: string;
    /** 发票 */
    invoice?: string;
    /** 费用类型 */
    type?: string;
    /** 金额 */
    money?: string;
    /** 实际报销类型 */
    actualType?: string;
    /** 实际报销金额 */
    actualMoney?: string;
    /** 正当理由 */
    reason?: string;
    /** 备注 */
    remark?: string;
    /** 报销类型 */
    finaType?: string;
  };

  type BusiProjReq = {
    /** 工单ID */
    workId?: string;
    /** 对应工单 */
    workNumber?: string;
    /** 工单事件 */
    workDesc?: string;
    /** 出差事由 */
    why?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目号  */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 出发时间 */
    goDate?: string;
    /** 出发地 */
    leave?: string;
    /** 返程时间 */
    backDate?: string;
    /** 到达地 */
    arrive?: string;
    /** 停留天数 */
    days?: string;
    /** 补贴金额 */
    allowance?: string;
    /** 备注 */
    remark?: string;
  };

  type BusiProjResp = {
    /** 工单ID */
    workId?: string;
    /** 对应工单 */
    workNumber?: string;
    /** 工单事件 */
    workDesc?: string;
    /** 出差事由 */
    why?: string;
    /** 项目ID */
    projectId?: string;
    /** 项目号  */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 出发时间 */
    goDate?: string;
    /** 出发地 */
    leave?: string;
    /** 返程时间 */
    backDate?: string;
    /** 到达地 */
    arrive?: string;
    /** 停留天数 */
    days?: string;
    /** 补贴金额 */
    allowance?: string;
    /** 备注 */
    remark?: string;
  };

  type CalendarReq = {
    calendarMonth?: string;
  };

  type CalendarResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 考勤日期 */
    attendanceDate?: string;
    /** 休假类型id */
    leaveType?: string;
    /** 休假类型 */
    leaveTypeName?: string;
    /** 半天 */
    halfDate?: string;
  };

  type cancelClaimParams = {
    receiptId: string;
  };

  type cancelInvoicingParams = {
    idReq: IdReq;
  };

  type cancelPayApplicationParams = {
    idReq: IdReq;
  };

  type CancelPaymentReq = {
    /** ID */
    id?: string;
    /** 描述 */
    cancelDesc?: string;
  };

  type CertificateReq = {
    /** 主键 */
    id?: string;
    /** 证书名称 */
    cerName?: string;
    /** 公司名称 */
    companyName?: string;
    /** 证书编号 */
    cerNumber?: string;
    /** 证书类型 */
    type?: string;
    /** 用户id */
    userId?: string;
    /** 用户名 */
    userName?: string;
    /** 取证时间 */
    receiveTime?: string;
    /** 有效期 */
    valTime?: string;
    /** 证书有效期 */
    grade?: string;
  };

  type CertificateResp = {
    /** 主键 */
    id?: string;
    /** 证书名称 */
    cerName?: string;
    /** 公司名称 */
    companyName?: string;
    /** 证书编号 */
    cerNumber?: string;
    /** 证书类型 */
    type?: string;
    /** 用户id */
    userId?: string;
    /** 用户名 */
    userName?: string;
    /** 取证时间 */
    receiveTime?: string;
    /** 有效期 */
    valTime?: string;
    /** 证书有效期 */
    grade?: string;
  };

  type CheckHolidayReq = {
    /** 需要校验的日期 */
    holidays: string;
  };

  type CheckSectionHolidayReq = {
    /** 假期开始时间 */
    startHolidays: string;
    /** 假期结束时间 */
    endHolidays: string;
  };

  type ChildNode = {
    id?: string;
    parentId?: string;
    type?: string;
    name?: string;
    desc?: string;
    props?: Properties;
    children?: ChildNode;
    branchs?: ChildNode[];
    parallelStr?: string;
    incoming?: { innerMap?: Record<string, any>; empty?: boolean };
    typeElse?: boolean;
  };

  type ClientMangers = {
    /** 客户经理主键 */
    clientMangerId?: string;
    /** 客户经理姓名 */
    clientMangerName?: string;
  };

  type CollectionBankReq = {
    /** 开票银行别名 */
    collectionBank?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 开户银行和银行账户 */
    accountAndBank?: string;
    /** 注册地址和联系电话 */
    contact?: string;
  };

  type CollectionBankResp = {
    /** 开票银行别名 */
    collectionBank?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 开户银行和银行账户 */
    accountAndBank?: string;
    /** 注册地址和联系电话 */
    contact?: string;
  };

  type CollectionInfoResp = {
    /** 合同总金额 */
    contractAmount?: number;
    /** 已开票金额 */
    billedAmount?: number;
    /** 已收款金额 */
    receivedAmount?: number;
    /** 开票总金额 */
    billTotalAmount?: number;
    /** 待开票金额 */
    awaitAmount?: number;
    /** 待收款金额 */
    awaitReAmount?: number;
  };

  type CollectionPlanReq = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 预计收款时间 */
    estimateReTime?: string;
    /** 预计收款金额 */
    estimateReAmount?: number;
    /** 支付条件 */
    payCondition?: string;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 不含税金额 */
    excludeRaAmount?: number;
    /** 收款方式 */
    collectionWay?: string;
    /** 开票流水号 */
    invoicingNumber?: string;
    /** 开票时间 */
    ticketTime?: string;
    /** 开票金额 */
    ticketAmount?: number;
    /** 收款状态 */
    collectionStatus?: string;
    /** 状态 */
    status?: string;
    /** 到款金额 */
    receiveAmount?: number;
    /** 到款时间 */
    receiveTime?: string;
    /** 认领日期 */
    claimTime?: string;
    /** 认领人主键 */
    claimUserId?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 收款流水主键 */
    receiptId?: string;
    /** 收款流水号 */
    collectNumber?: string;
    /** 操作状态 */
    operateStatus?: string;
  };

  type CollectionPlanResp = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 预计收款时间 */
    estimateReTime?: string;
    /** 预计收款金额 */
    estimateReAmount?: number;
    /** 支付条件 */
    payCondition?: string;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 不含税金额 */
    excludeRaAmount?: number;
    /** 收款方式 */
    collectionWay?: string;
    /** 开票流水号 */
    invoicingNumber?: string;
    /** 开票时间 */
    ticketTime?: string;
    /** 开票金额 */
    ticketAmount?: number;
    /** 收款状态 */
    collectionStatus?: string;
    /** 状态 */
    status?: string;
    /** 到款金额 */
    receiveAmount?: number;
    /** 到款时间 */
    receiveTime?: string;
    /** 认领日期 */
    claimTime?: string;
    /** 认领人主键 */
    claimUserId?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 收款流水主键 */
    receiptId?: string;
    /** 收款流水号 */
    collectNumber?: string;
    /** 操作状态 */
    operateStatus?: string;
  };

  type CollectTicketClaimReq = {
    /** 收票id */
    id?: string;
    /** 合同id */
    contractId?: string;
    /** 合同类型 采购 PC 内部 IC */
    contractType?: string;
  };

  type CollectTicketContractListReq = {
    /** 单位名称 */
    institutionName?: string;
  };

  type CollectTicketContractListResp = {
    /** 合同ID */
    id?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同类型 */
    contractType?: string;
  };

  type CollectTicketContractResp = {
    /** 收票ID */
    id?: string;
    /** 收票流水号 */
    documentNumber?: string;
    /** 认领日期 */
    assignDate?: string;
    /** 开票日期 */
    invoiceDate?: string;
    /** 发票类型 */
    invoiceType?: string;
    /** 票据类型 */
    billType?: string;
    /** 价税合计 */
    totalAmount?: number;
    /** 金额 */
    amountOfMoney?: number;
    /** 税额 */
    taxAmount?: number;
    /** 备注 */
    remarks?: string;
  };

  type CollectTicketInfoResp = {
    /** 收票id */
    id?: string;
    /** 收票流水号 */
    documentNumber?: string;
    /** 合同id */
    contractId?: string;
    /** 合同号 */
    contractNumber?: string;
    /** 合同类型 */
    contractType?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 合同名称 */
    contractName?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 发票号 */
    invoiceNumber?: string;
    /** 发票类型 */
    invoiceType?: string;
    /** 价税合计 */
    totalAmount?: number;
    /** 票据类型 */
    billType?: string;
    /** 金额 */
    amountOfMoney?: number;
    /** 开票日期 */
    invoiceDate?: string;
    /** 合计税额 */
    taxAmount?: number;
    /** 备注 */
    remarks?: string;
    /** 领票日期 */
    assignDate?: string;
    /** 领票人 */
    assignPersonName?: string;
    /** 领票人id */
    assignPersonId?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 开票明细 */
    invoiceNumberList?: InvoiceNumberDTO[];
  };

  type CollectTicketInsertReq = {
    /** 单位名称 */
    institutionName?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 发票类型 */
    invoiceType?: string;
    /** 价税合计 */
    totalAmount?: number;
    /** 票据类型 */
    billType?: string;
    /** 合计金额 */
    amountOfMoney?: number;
    /** 开票日期 */
    invoiceDate?: string;
    /** 合计税额 */
    taxAmount?: number;
    /** 备注 */
    remarks?: string;
    /** 开票明细 */
    invoiceNumberList?: InvoiceNumberDTO[];
  };

  type CollectTicketPageResp = {
    /** 收票ID */
    id?: string;
    /** 收票流水号 */
    documentNumber?: string;
    /** 领票人 */
    assignPersonName?: string;
    /** 领票人id */
    assignPersonId?: string;
    /** 领票日期 */
    assignDate?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 价税合计 */
    totalAmount?: number;
    /** 开票日期 */
    invoiceDate?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 合同id */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同号 */
    contractNumber?: string;
  };

  type CollectTicketUpdateReq = {
    /** 收票id */
    id?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 发票类型 */
    invoiceType?: string;
    /** 价税合计 */
    totalAmount?: number;
    /** 票据类型 */
    billType?: string;
    /** 合计金额 */
    amountOfMoney?: number;
    /** 开票日期 */
    invoiceDate?: string;
    /** 合计税额 */
    taxAmount?: number;
    /** 备注 */
    remarks?: string;
    /** 开票明细 */
    invoiceNumberList?: InvoiceNumberDTO[];
  };

  type CommonlyFuncUpdateReq = {
    /** 常用功能 */
    commonlyFunctions?: string[];
  };

  type ConditionInfo = {
    id?: string;
    title?: string;
    valueType?: string;
    compare?: string;
    value?: Record<string, any>[];
  };

  type ContractContentReq = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 该条内容创建时间 */
    addTime?: string;
    /** 内容 */
    content?: string;
  };

  type ContractContentResp = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 该条内容创建时间 */
    addTime?: string;
    /** 创建人 */
    content?: string;
  };

  type ContractPaymentResp = {
    /** 合同主键 */
    id?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方名称 */
    fpName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方名称 */
    spName?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 最终用户名称 */
    endUser?: string;
  };

  type ContractPayResp = {
    id?: string;
    contractNumber?: string;
    contractName?: string;
    contractId?: string;
    estimatePayTime?: string;
    estimatePayAmount?: number;
    payWay?: string;
    payStatus?: string;
    receiptAmount?: number;
    countInvoicedTime?: string;
    applicationNumber?: string;
  };

  type ContractProfitResp = {
    /** 合同主键 */
    id?: string;
    /** 合同号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 销售 */
    salePerson?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 已收款 */
    amountCollected?: number;
    /** 采购成本 */
    purchaseCost?: number;
    /** 缴税成本 */
    taxCost?: number;
    /** 售前项目成本 */
    preCost?: number;
    /** 销售项目成本 */
    saleCost?: number;
    /** 交付成本 */
    deliveryCost?: number;
    /** 资金成本 */
    fundCost?: number;
    /** 费用成本 */
    feesCost?: number;
    /** 利润 */
    profit?: number;
    /** 利润比 */
    profitRatio?: number;
  };

  type ContractReceiptsAndPaymentsResp = {
    /** 对应主合同的收款信息 */
    collectionPlanList?: CollectionPlanResp[];
    /** 采购合同计划详细信息表 */
    payPlanDetailInfoList?: PayPlanDetailInfoResp[];
  };

  type CoopAgreeReq = {
    /** 主键 */
    id?: string;
    /** 协议编号 */
    contractNumber?: string;
    /** 协议类别 */
    agreementCategory?: string;
    /** 协议类型 */
    agreementType?: string;
    /** 协议状态 */
    agreementStatus?: string;
    /** 协议名称 */
    agreementName?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 对方协议编号 */
    counterpartAgreeNum?: string;
    /** 协议内容 */
    agreementContent?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
  };

  type CoopAgreeResp = {
    /** 主键 */
    id?: string;
    /** 编号规则 */
    serialNumberRule?: string;
    /** 协议编号 */
    agreementNumber?: string;
    /** 协议类别 */
    agreementCategory?: string;
    /** 协议类型 */
    agreementType?: string;
    /** 协议状态 */
    agreementStatus?: string;
    /** 协议名称 */
    agreementName?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 对方协议编号 */
    counterpartAgreeNum?: string;
    /** 协议内容 */
    agreementContent?: string;
  };

  type CurrentUserWorkReq = {
    /** 项目类型 */
    projectType?: string;
  };

  type CurrentUserWorkResp = {
    /** 工单id */
    workId?: string;
    /** 工单编号 */
    workNumber?: string;
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
  };

  type dataParsingParams = {
    req: ImportDataReq;
  };

  type DatePageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 子部门 */
    departmentBranchId?: string;
    /** 员工ID */
    employeeId?: string;
    /** 总工时排序字段 */
    sortTotalHours?: string;
    /** 汇总人 */
    sortEmployeeName?: string;
    /** NB工时 */
    sortNb?: string;
    /** SH工时 */
    sortSh?: string;
    /** SQ工时 */
    sortSq?: string;
    /** 其他工时 */
    sortOther?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
  };

  type DayInfoReq = {
    /** 日期 */
    time?: string;
    /** 发票 */
    invoice?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 费用类型 */
    type?: string;
    /** 金额 */
    money?: string;
    /** 实际报销类型 */
    actualType?: string;
    /** 实际报销金额 */
    actualMoney?: string;
    /** 正当理由 */
    reason?: string;
    /** 备注 */
    remark?: string;
    /** 报销类型 */
    finaType?: string;
  };

  type DayInfoResp = {
    /** 日期 */
    time?: string;
    /** 发票 */
    invoice?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 费用类型 */
    type?: string;
    /** 金额 */
    money?: string;
    /** 实际报销类型 */
    actualType?: string;
    /** 实际报销金额 */
    actualMoney?: string;
    /** 正当理由 */
    reason?: string;
    /** 备注 */
    remark?: string;
    /** 报销类型 */
    finaType?: string;
  };

  type DepartmentAndUserTreeResp = {
    /** ID */
    id?: string;
    /** 部门名称 */
    name?: string;
    /** 子集 */
    child?: DepartmentAndUserTreeResp[];
  };

  type DepartmentIdReq = {
    /** 部门id 值为null 或空字符串查询所有部门 */
    id?: string;
  };

  type DepartmentInsertReq = {
    /** 部门 */
    departmentName: string;
    /** 上级部门 */
    parentId: string;
    /** 部门状态 */
    status?: string;
    /** 部门领导 */
    leaders?: Leader[];
    /** 部门主管 */
    departmentHead?: string;
    /** 当前部门是否生成周报 */
    weekly?: string;
  };

  type DepartmentListResp = {
    /** ID */
    id?: string;
    /** 上级部门id */
    parentId?: string;
    /** 部门名称 */
    departmentName?: string;
  };

  type DepartmentTreeResp = {
    /** ID */
    id?: string;
    /** 部门名称 */
    departmentName?: string;
    /** 上级部门ID */
    parentId?: string;
    /** 状态 */
    status?: string;
    /** 当前部门是否生成周报 */
    weekly?: string;
    /** 子部门 */
    child?: DepartmentTreeResp[];
    /** 部门主管 */
    departmentHead?: string;
    /** 部门领导 */
    leaders?: Leader[];
    /** 部门成员 */
    members?: Member[];
  };

  type DepartmentUpdateReq = {
    /** ID */
    id: string;
    /** 部门 */
    departmentName: string;
    /** 上级部门 */
    parentId: string;
    /** 部门状态 */
    status?: string;
    /** 部门领导 */
    leaders?: Leader[];
    /** 部门主管 */
    departmentHead?: string;
    /** 成员 */
    members?: Member[];
    /** 备注 */
    remark?: string;
    /** 当前部门是否生成周报 */
    weekly?: string;
  };

  type DepartmentWeekTreeResp = {
    /** ID */
    id?: string;
    /** 部门名称 */
    departmentName?: string;
    /** 上级部门ID */
    parentId?: string;
    /** 子部门 */
    child?: DepartmentWeekTreeResp[];
  };

  type Dict = {
    empty?: boolean;
  };

  type dictionaryDeleteParams = {
    req: IdReq;
  };

  type DictionaryInfoResp = {
    dictionaryType?:
      | 'GENDER'
      | 'NATION'
      | 'WORK_STATUS'
      | 'ATTENDANCE_STATUS'
      | 'MARITAL_STATUS'
      | 'POLITICS'
      | 'TOP_EDUCATION'
      | 'ACTIVATION_STATUS';
    key?: string;
    value?: string;
  };

  type dictionaryList1Params = {
    req: DictionaryListReq;
  };

  type dictionaryListParams = {
    req: IdReq;
  };

  type DictionaryListReq = {
    /** GENDER:性别,NATION:民族,TOP_EDUCATION:学历,POLITICS:政治面貌,MARITAL_STATUS:婚姻状况,WORK_STATUS:岗位状态,ATTENDANCE_STATUS:考勤状态 */
    type?:
      | 'GENDER'
      | 'NATION'
      | 'WORK_STATUS'
      | 'ATTENDANCE_STATUS'
      | 'MARITAL_STATUS'
      | 'POLITICS'
      | 'TOP_EDUCATION'
      | 'ACTIVATION_STATUS';
  };

  type DictionaryListResp = {
    dictionaryType?:
      | 'GENDER'
      | 'NATION'
      | 'WORK_STATUS'
      | 'ATTENDANCE_STATUS'
      | 'MARITAL_STATUS'
      | 'POLITICS'
      | 'TOP_EDUCATION'
      | 'ACTIVATION_STATUS';
    key?: string;
    value?: string;
  };

  type DictionaryReq = {
    /** GENDER:性别,NATION:民族,TOP_EDUCATION:学历,POLITICS:政治面貌,MARITAL_STATUS:婚姻状况,WORK_STATUS:岗位状态,ATTENDANCE_STATUS:考勤状态 */
    type?:
      | 'GENDER'
      | 'NATION'
      | 'WORK_STATUS'
      | 'ATTENDANCE_STATUS'
      | 'MARITAL_STATUS'
      | 'POLITICS'
      | 'TOP_EDUCATION'
      | 'ACTIVATION_STATUS';
    /** key */
    key?: string;
  };

  type EmailReq = {
    email?: string;
  };

  type EmployeeGradeInsertReq = {
    /** 福利等级 */
    grade: string;
    /** 补贴总金额 */
    count: string;
    subsidyForms?: SubsidyForm[];
  };

  type EmployeeGradeListResp = {
    /** ID */
    id?: string;
    /** 福利等级 */
    grade?: string;
    /** 补贴总金额 */
    count?: string;
    /** 补贴构成 */
    subsidyForms?: SubsidyForm[];
  };

  type EmployeeGradeResp = {
    /** 福利等级 */
    grade?: string;
    /** 补贴总金额 */
    count?: string;
    /** 补贴构成 */
    subsidyForms?: SubsidyForm[];
  };

  type EmployeeGradeUpdateReq = {
    /** ID */
    id: string;
    /** 福利等级 */
    grade: string;
    /** 补贴总金额 */
    count: string;
    subsidyForms?: SubsidyForm[];
  };

  type employeeInfoParams = {
    req: IdReq;
  };

  type EmployeeInfoResp = {
    /** ID */
    id?: string;
    /** 姓名 */
    employeeName?: string;
    /** 性别 */
    gender?: string;
    /** 员工类别 */
    employmentType?: string;
    /** 工作地点 */
    workPlace?: string;
    /** 年龄 */
    age?: string;
    /** 民族 */
    nation?: string;
    /** 出生日期 */
    dateOfBirth?: string;
    /** 身份证号 */
    personId?: string;
    /** 籍贯 */
    nativePlace?: string;
    /** 最高学历 */
    topEducation?: string;
    /** 毕业时间 */
    graduationDate?: string;
    /** 毕业院校 */
    graduationSchool?: string;
    /** 政治面貌 */
    politics?: string;
    /** 婚姻状况 */
    maritalStatus?: string;
    /** 居住地址 */
    currentAddress?: string;
    /** 紧急联系人 */
    personToBeContacted?: string;
    /** 联系人关系 */
    relation?: string;
    /** 联系人方式1 */
    emergencyPhoneNumber?: string;
    /** 联系人方式2 */
    rgencyPhoneNumber2?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 激活状态 */
    status?: string;
    /** 部门 */
    department?: string;
    /** 子部门 */
    departmentBranch?: string;
    /** 第二部门 */
    secondDepartment?: string;
    /** 社保缴纳地 */
    socialSecurity?: string;
    /** 职位 */
    designation?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 公司邮箱 */
    companyEmail?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 初始工作时间 */
    firstWorkTime?: string;
    /** 专业 */
    major?: string;
    /** 技术特长 */
    technicalExpertise?: string;
    /** 入职日期 */
    dateOfJoining?: string;
    /** 转正日期 */
    importantTime?: string;
    /** 基本工资 */
    basicSalary?: string;
    /** 福利等级 */
    grade?: string;
    /** 岗位状态 */
    workStatus?: string;
    /** 离职日期 */
    relievingDate?: string;
    /** 关联用户账号 */
    userId?: string;
    /** 是应届毕业生 */
    isStu?: string;
    /** 开户行 */
    accountOpening?: string;
    /** 银行账号 */
    bankAcNo?: string;
    /** 劳动合同编号 */
    contractNum?: string;
    /** 合同开始时间 */
    contractStartDate?: string;
    /** 合同结束时间 */
    contractEndDate?: string;
    /** 公司 */
    company?: string;
    /** 参加工作年限 */
    totalWorkYears?: string;
    /** 公司在职年限 */
    rkWorkYears?: string;
  };

  type EmployeeUpdateReq = {
    /** ID */
    id?: string;
    /** 姓名 */
    employeeName?: string;
    /** 性别 */
    gender?: string;
    /** 员工类别-id */
    employmentType?: string;
    /** 工作地点 */
    workPlace?: string;
    /** 年龄 */
    age?: string;
    /** 民族 */
    nation?: string;
    /** 出生日期 */
    dateOfBirth?: string;
    /** 身份证号 */
    personId?: string;
    /** 籍贯 */
    nativePlace?: string;
    /** 最高学历 */
    topEducation?: string;
    /** 毕业时间 */
    graduationDate?: string;
    /** 毕业院校 */
    graduationSchool?: string;
    /** 政治面貌 */
    politics?: string;
    /** 婚姻状况 */
    maritalStatus?: string;
    /** 居住地址 */
    currentAddress?: string;
    /** 紧急联系人 */
    personToBeContacted?: string;
    /** 联系人关系 */
    relation?: string;
    /** 联系人方式1 */
    emergencyPhoneNumber?: string;
    /** 联系人方式2 */
    rgencyPhoneNumber2?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 激活状态 */
    status?: string;
    /** 部门-id */
    department?: string;
    /** 子部门-id */
    departmentBranch?: string;
    /** 第二部门-id */
    secondDepartment?: string;
    /** 社保缴纳地 */
    socialSecurity?: string;
    /** 职位-id */
    designation?: string;
    /** 直属上级姓名-id */
    reportsToName?: string;
    /** 公司邮箱 */
    companyEmail?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 初始工作时间 */
    firstWorkTime?: string;
    /** 专业 */
    major?: string;
    /** 技术特长 */
    technicalExpertise?: string;
    /** 入职日期 */
    dateOfJoining?: string;
    /** 转正日期 */
    importantTime?: string;
    /** 基本工资 */
    basicSalary?: string;
    /** 福利等级 */
    grade?: string;
    /** 岗位状态 */
    workStatus?: string;
    /** 离职日期 */
    relievingDate?: string;
    /** 关联用户账号 */
    userId?: string;
    /** 是应届毕业生 */
    isStu?: string;
    /** 开户行 */
    accountOpening?: string;
    /** 银行账号 */
    bankAcNo?: string;
    /** 劳动合同编号 */
    contractNum?: string;
    /** 合同开始时间 */
    contractStartDate?: string;
    /** 合同结束时间 */
    contractEndDate?: string;
    /** 公司 */
    company?: string;
    /** 参加工作年限 */
    totalWorkYears?: string;
    /** 公司在职年限 */
    rkWorkYears?: string;
  };

  type EmploymentTypeInsertReq = {
    /** 员工类型 */
    employeeTypeName: string;
  };

  type EmploymentTypeListResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 员工类型 */
    employeeTypeName?: string;
  };

  type EmploymentTypeUpdateReq = {
    /** ID */
    id?: string;
    /** 员工类型 */
    employeeTypeName?: string;
  };

  type ExecuteContractResp = {
    /** 主合同主键 */
    contractId?: string;
    /** 主合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同类别 */
    contractType?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同总金额 */
    contractAmount?: number;
    /** 开票总金额 */
    billedAmount?: number;
    /** 待开票金额 */
    awaitAmount?: number;
    /** 收款金额 */
    receivedAmount?: number;
    /** 代收款金额 */
    awaitReAmount?: number;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 销售人员 */
    salePerson?: string;
    /** 采购合同主键 */
    purContractId?: string;
    /** 采购合同号编号 */
    purContractNumber?: string;
    /** 备注 */
    remark?: string;
  };

  type ExportContractReq = {
    startTime?: string;
    endTime?: string;
  };

  type ExportDepartmentResp = {
    name?: string;
    parent?: string;
  };

  type exportInnerParams = {
    req: ExportContractReq;
  };

  type exportMainParams = {
    req: ExportContractReq;
  };

  type exportPurParams = {
    req: ExportContractReq;
  };

  type ExportUserResp = {
    username?: string;
    phone?: string;
    email?: string;
    department?: string;
    departmentParent?: string;
  };

  type FeesDetailReq = {
    /** 成本id */
    sqFeesId?: string;
    /** 项目 id */
    projectId?: string;
  };

  type FileAddReq = {
    fileType?: string;
    file?: string;
  };

  type FileNameReq = {
    fileName?: string;
  };

  type FinaCountReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    /** 项目 */
    projectName?: string;
    /** 部门 */
    departName?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
  };

  type FinaCountResp = {
    page?: IPageFinalDetailsResp;
    /** 累计报销金额 */
    busiCount?: number;
    /** 实际报销金额 */
    actualCount?: number;
  };

  type finaInfoParams = {
    req: IdReq;
  };

  type FinaInfoResp = {
    /** ID */
    id?: string;
    /** 编号 */
    documentNumber?: string;
    /** 公司 */
    company?: string;
    /** 员工ID */
    employeeId?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 部门 */
    department?: string;
    /** 子部门  */
    departmentBranch?: string;
    /** 电子邮件 */
    email?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 报销类型 */
    type?: string;
    /** 报销日期 */
    finaDate?: string;
    /** 预支金额 */
    advance?: string;
    /** 直属上级编号 */
    reportsToNumber?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 报销总金额 */
    total?: string;
    /** 实际总金额 */
    actualTotal?: string;
    /** 补贴金额 */
    allowance?: string;
    /** 报销金额 */
    busi?: string;
    /** 实际金额 */
    actual?: string;
    /** 差旅报销明细 */
    busiInfoList?: BusiInfoResp[];
    /** 差旅信息明细 */
    busiProjList?: BusiProjResp[];
    /** 日常报销明细 */
    dayInfoList?: DayInfoResp[];
    /** 审批状态 */
    activiStatus?: string;
  };

  type FinaInsertReq = {
    /** 差旅报销明细 */
    busiInfoList?: BusiInfoReq[];
    /** 差旅信息明细 */
    busiProjList?: BusiProjReq[];
    /** 日常报销明细 */
    dayInfoList?: DayInfoReq[];
    /** 编号 */
    documentNumber?: string;
    /** 编号模板 */
    serialNumberRule?: string;
    /** 公司 */
    company?: string;
    /** 员工ID */
    employeeId?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 部门 */
    department?: string;
    /** 子部门  */
    departmentBranch?: string;
    /** 电子邮件 */
    email?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 报销类型 */
    type?: string;
    /** 报销日期 */
    finaDate?: string;
    /** 预支金额 */
    advance?: string;
    /** 直属上级编号 */
    reportsToNumber?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 报销总金额 */
    total?: string;
    /** 实际总金额 */
    actualTotal?: string;
    /** 补贴金额 */
    allowance?: string;
    /** 报销金额 */
    busi?: string;
    /** 实际金额 */
    actual?: string;
  };

  type FinalDetailsResp = {
    /** 项目名称 */
    projectName?: string;
    /** 项目ID */
    projectId?: string;
    /** 报销人 */
    username?: string;
    /** 部门 */
    departmentName?: string;
    /** 报销ID */
    finaId?: string;
    /** 报销单号 */
    finaNumber?: string;
    /** 报销类型 */
    finaType?: string;
    /** 发生日期 */
    finaDate?: string;
    /** 费用类型 */
    type?: string;
    /** 报销金额 */
    busi?: string;
    /** 实际报销类型 */
    actualType?: string;
    /** 实际金额 */
    actual?: string;
  };

  type FinaPageResp = {
    /** ID */
    id?: string;
    /** 编号 */
    documentNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 报销类型 */
    type?: string;
    /** 报销日期 */
    finaDate?: string;
    /** 报销总金额 */
    total?: string;
    /** 实际总金额 */
    actualTotal?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 审批人 */
    currentAudiUsers?: string[];
  };

  type FinaSubsidyReq = {
    /** 出发时间 */
    goDate?: string;
    /** 返程时间 */
    backDate?: string;
  };

  type FinaUpdateReq = {
    /** 差旅报销明细 */
    busiInfoList?: BusiInfoReq[];
    /** 差旅信息明细 */
    busiProjList?: BusiProjReq[];
    /** 日常报销明细 */
    dayInfoList?: DayInfoReq[];
    /** ID */
    id?: string;
    /** 编号 */
    documentNumber?: string;
    /** 编号模板 */
    serialNumberRule?: string;
    /** 公司 */
    company?: string;
    /** 员工ID */
    employeeId?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 部门 */
    department?: string;
    /** 子部门 */
    departmentBranch?: string;
    /** 电子邮件 */
    email?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 报销类型 */
    type?: string;
    /** 报销日期 */
    finaDate?: string;
    /** 预支金额 */
    advance?: string;
    /** 直属上级编号 */
    reportsToNumber?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 报销总金额 */
    total?: string;
    /** 实际总金额 */
    actualTotal?: string;
    /** 补贴金额 */
    allowance?: string;
    /** 报销金额 */
    busi?: string;
    /** 实际金额 */
    actual?: string;
  };

  type FindProjectByContractReq = {
    /** 合同ID */
    id?: string;
    /** 项目类型 */
    projectType?: string;
  };

  type FirmResourceConfirmReq = {
    /** ID */
    id?: string;
    /** 最终归还时间 */
    ultimatelyStillTime?: string;
    /** 逾期原因 */
    lateCause?: string;
  };

  type FirmResourceInfoResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 内容 */
    content?: string;
    /** 资源所属部门 */
    resourceDepartment?: string;
    /** 资源所属部门 */
    resourceDepartmentName?: string;
    /** 使用时间 */
    useTime?: string;
    /** 归还时间 */
    stillTime?: string;
    /** 使用原因 */
    useCause?: string;
    /** 最终归还时间 */
    ultimatelyStillTime?: string;
    /** 逾期原因 */
    lateCause?: string;
    /** 资源状态 */
    causeStatus?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type FirmResourceInsertReq = {
    /** 内容 */
    content?: string;
    /** 资源所属部门 */
    resourceDepartment?: string;
    /** 使用时间 */
    useTime?: string;
    /** 归还时间 */
    stillTime?: string;
    /** 使用原因 */
    useCause?: string;
    /** 最终归还时间 */
    ultimatelyStillTime?: string;
    /** 逾期原因 */
    lateCause?: string;
    /** 资源状态 */
    causeStatus?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
  };

  type FirmResourceUpdateReq = {
    /** ID */
    id?: string;
    /** 内容 */
    content?: string;
    /** 资源所属部门 */
    resourceDepartment?: string;
    /** 使用时间 */
    useTime?: string;
    /** 归还时间 */
    stillTime?: string;
    /** 使用原因 */
    useCause?: string;
    /** 最终归还时间 */
    ultimatelyStillTime?: string;
    /** 逾期原因 */
    lateCause?: string;
    /** 资源状态 */
    causeStatus?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
  };

  type FlowEngineDTO = {
    formId?: string;
    formItems?: string;
    formName?: string;
    groupId?: number;
    logo?: string;
    process?: ChildNode;
    remark?: string;
    settings?: string;
  };

  type FlowPagePaymentResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PaymentResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
    /** 已付款金额 */
    payedAmount?: number;
    /** 待付款金额 */
    awaitPayAmount?: number;
    /** 已退款金额 */
    refundAmount?: number;
    /** 未退款金额 */
    awaitRefundAmount?: number;
  };

  type FlowPageRefundResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: RefundResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
    /** 已付款金额 */
    payedAmount?: number;
    /** 待付款金额 */
    awaitPayAmount?: number;
    /** 已退款金额 */
    refundAmount?: number;
    /** 未退款金额 */
    awaitRefundAmount?: number;
  };

  type FormOperates = {
    id?: string;
    title?: string;
    required?: boolean;
    perm?: string;
  };

  type getAptitudeByIdParams = {
    idReq: IdReq;
  };

  type getCoopAgreeByIdParams = {
    idReq: IdReq;
  };

  type getDataDictionaryParams = {
    req: DictionaryReq;
  };

  type getFileParams = {
    fileName: string;
  };

  type getInnerConByIdParams = {
    idReq: IdReq;
  };

  type getMainConByIdParams = {
    idReq: IdReq;
  };

  type getPurConByIdParams = {
    idReq: IdReq;
  };

  type getTenderByIdParams = {
    idReq: IdReq;
  };

  type getUserScheduleListParams = {
    /** 日程时间 */
    dateTime: string;
  };

  type gradeInfoParams = {
    req: IdReq;
  };

  type GroupsInfo = {
    groupType?: string;
    conditions?: ConditionInfo[];
    cids?: string[];
  };

  type HistoricalContactsResp = {
    /** 联系人姓名 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
  };

  type HistoricProcessInstancePageResp = {
    historicProcessInstanceRespList?: HistoricProcessInstanceResp[];
    count?: number;
  };

  type HistoricProcessInstanceResp = {
    processInstanceId?: string;
    documentNumber?: Record<string, any>;
    startUsername?: string;
    activiStatus?: string;
    authUsers?: string[];
    authNodeName?: string;
    startTime?: string;
    extra?: Record<string, any>;
  };

  type HolidayDetailsInsertReq = {
    /** 假期表id */
    holidayId?: number;
    /** 日期 */
    holidays?: string;
    /** 描述 */
    describe?: string;
  };

  type HolidayDetailsListResp = {
    /** ID */
    id?: string;
    /** 日期 */
    holidays?: string;
    /** 描述 */
    describe?: string;
  };

  type HolidayDetailsPageResp = {
    /** ID */
    id?: string;
    /** 日期 */
    holidays?: string;
    /** 描述 */
    describe?: string;
  };

  type HolidayDetailsUpdateReq = {
    /** ID */
    id?: string;
    /** 假期表id */
    holidayId?: string;
    /** 日期 */
    holidays?: string;
    /** 描述 */
    describe?: string;
  };

  type holidayInfoParams = {
    req: IdReq;
  };

  type HolidayInfoResp = {
    /** 假期详情表 */
    holidayDetailsRespList?: HolidayDetailsListResp[];
    /** ID */
    id?: string;
    /** 假期表名称 */
    holidayListName?: string;
    /** 开始时间 */
    startDate?: string;
    /** 结束时间 */
    endDate?: string;
    /** 状态 */
    status?: string;
  };

  type HolidayInsertDetailsReq = {
    /** 日期 */
    holidays?: string;
    /** 描述 */
    describe?: string;
  };

  type HolidayInsertReq = {
    /** 假期表名称 */
    holidayListName?: string;
    /** 开始时间 */
    startDate?: string;
    /** 结束时间 */
    endDate?: string;
    /** 状态 0禁用1启用 */
    status?: string;
    /** 详情列表 */
    detailsList?: HolidayInsertDetailsReq[];
  };

  type HolidayPageResp = {
    /** ID */
    id?: string;
    /** 假期表名称 */
    holidayListName?: string;
    /** 开始时间 */
    startDate?: string;
    /** 结束时间 */
    endDate?: string;
    /** 状态 */
    status?: string;
  };

  type HolidayUpdateReq = {
    /** ID */
    id?: string;
    /** 假期表名称 */
    holidayListName?: string;
    /** 开始时间 */
    startDate?: string;
    /** 结束时间 */
    endDate?: string;
    /** 状态 */
    status?: string;
    /** 详情列表 */
    holidayDetailsRespList?: HolidayInsertDetailsReq[];
  };

  type HomeTreatSubmitReq = {
    /** 页数 */
    pageNum?: number;
    /** 每页多少条 */
    pageSize?: number;
    /** 编号模糊查询 */
    searchNumber?: string;
    /** 类型筛选 */
    searchType?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type HomeWorkFlowReq = {
    /** 流程类型 */
    type?: string;
    /** 页数 */
    pageNum?: number;
    /** 每页多少条 */
    pageSize?: number;
    /** 编号模糊查询 */
    searchNumber?: string;
    /** 申请人模糊查询 */
    searchStartUsername?: string;
  };

  type IdReq = {
    /** ID */
    id: string;
  };

  type IdsReq = {
    /** ID */
    ids: string[];
  };

  type importByTableNameParams = {
    req: ImportDataReq;
  };

  type ImportDataReq = {
    /** 表名 */
    tableName?: string;
    file?: string;
  };

  type importUserParams = {
    file: string;
  };

  type InitialPerformanceAppraisalTableResp = {
    /** 合同ID */
    id?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 核算编号 */
    documentNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 开始时间 */
    startTime?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 年份 */
    receiveYear?: string;
    /** 核算状态 */
    performanceStatus?: string;
  };

  type InnerConInfoReq = {
    /** 主键 */
    id?: string;
    /** 服务类别 */
    serveCategory?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 执行部门 */
    execDepartment?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 税额 */
    contractTax?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 备注 */
    remark?: string;
    /** 内容概述 */
    overview?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 项目经理 */
    proManger?: string;
    /** 项目经理主键 */
    proMangerId?: string;
    /** 税率 */
    rate?: number;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 计划交货日期 */
    estimatedDeliDate?: string;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
  };

  type InnerConInfoResp = {
    /** 主键 */
    id?: string;
    /** 合同类别 */
    contractCategory?: string;
    /** 编号规则 */
    serialNumberRule?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 执行部门 */
    execDepartment?: string;
    /** 合同主键 */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 服务类别 */
    serveCategory?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 税额 */
    contractTax?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 项目经理 */
    proManger?: string;
    /** 销售经理主键 */
    proMangerId?: string;
    /** 税率 */
    rate?: number;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 内容概述 */
    overview?: string;
    /** 预计交货日期 */
    estimatedDeliDate?: string;
    /** 备注 */
    remark?: string;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type InnerConReq = {
    innerConInfo?: InnerConInfoReq;
    /** 合同内请求参数集合 */
    contractContentList?: ContractContentReq[];
    payInfo?: PayInfoReq;
    /** 支付计划详细信息 */
    payPlanDetailInfoList?: PayPlanDetailInfoReq[];
  };

  type InnerContractResp = {
    innerConInfo?: InnerConInfoResp;
    /** 合同内容响应参数集合 */
    contractContentList?: ContractContentResp[];
    paySummary?: PaySummaryResp;
    payInfo?: PayInfoResp;
    /** 付款计划详细信息表 */
    payPlanDetailInfoList?: PayPlanDetailInfoResp[];
    /** 合同支付流水响应对象集合 */
    contractPayList?: PaymentResp[];
    /** 合同发票记录响应对象集合 */
    collectTicketContractList?: CollectTicketContractResp[];
  };

  type InstitutionCustomerIdReq = {
    /** 单位id */
    institutionId?: string;
    /** 业务伙伴id */
    customerId?: string;
  };

  type InstitutionCustomerIdsReq = {
    /** 单位id */
    institutionId?: string;
    /** 业务伙伴id */
    customerId?: string[];
  };

  type InstitutionCustomerListResp = {
    id?: string;
    /** 父级id */
    parentId?: string;
    /** 业务伙伴名称 */
    customerName?: string;
    /** 销售 */
    salePerson?: string;
    /** 业务伙伴类别 */
    partnerType?: string;
  };

  type InstitutionEditSalePerssonReq = {
    /** 单位id */
    parentId: string;
    /** 业务伙伴 */
    salePerson?: string;
  };

  type InstitutionListResp = {
    id?: string;
    /** 单位名称 */
    institution?: string;
    /** 单位编号 */
    documentNumber?: string;
    /** 业务伙伴名称 */
    customerName?: string;
    /** 销售 */
    salePerson?: string;
    /** 业务伙伴列表 */
    customerList?: InstitutionCustomerListResp[];
  };

  type InvoiceDetailsDTO = {
    /** 开票内容 */
    content?: string;
    /** 型号规格 */
    model?: string;
    /** 单位 */
    unit?: string;
    /** 数量 */
    amount?: number;
    /** 金额 */
    money?: number;
    /** 税率 */
    taxRate?: number;
    /** 税额 */
    tax?: number;
  };

  type InvoicedPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortApplicationTime?: string;
    sortTicketTime?: string;
    sortTicketAmount?: string;
  };

  type InvoiceNumberDTO = {
    /** 开票号码 */
    invoiceNo?: string;
    /** 开票明细列表 */
    list?: InvoiceDetailsDTO[];
  };

  type InvoicingReq = {
    /** 主键 */
    id?: string;
    /** 关联收款计划 */
    collPlanId?: string;
    /** 开票流水号 */
    invoicingNumber?: string;
    /** 关联合同主键 */
    contractId?: string;
    /** 关联合同编号 */
    contractNumber?: string;
    /** 关联合同类型 */
    contractType?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 销售人员姓名 */
    salePerson?: string;
    /** 申请时间 */
    applicationTime?: string;
    /** 开票金额 */
    ticketAmount?: number;
    /** 开票时间 */
    ticketTime?: string;
    /** 开票类型 */
    ticketType?: string;
    /** 票据类型 */
    billType?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 银行账户 */
    account?: string;
    /** 收款银行 */
    collectionBank?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 注册地址 */
    registeredAddress?: string;
    /** 开票内容 */
    ticketContent?: string;
    /** 规格型号 */
    model?: string;
    /** 单位 */
    unit?: string;
    /** 数量 */
    quantity?: string;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 备注 */
    remark?: string;
    /** 状态 */
    status?: string;
    /** 操作状态 */
    operateStatus?: string;
    /** 销货单位名称 */
    salesFirm?: string;
    /** 销货纳税人识别号 */
    salesTaxpayerNum?: string;
    /** 地址、电话 */
    salesContact?: string;
    /** 开户行及账号 */
    salesAccount?: string;
    /** 开票人 */
    drawerName?: string;
    /** 开票人id */
    drawerId?: string;
    /** 价税合计 */
    ticketAndRate?: number;
    /** 开票明细 */
    invoiceNumberList?: InvoiceNumberDTO[];
  };

  type InvoicingResp = {
    /** 主键 */
    id?: string;
    /** 关联收款计划 */
    collPlanId?: string;
    /** 开票流水号 */
    invoicingNumber?: string;
    /** 关联合同主键 */
    contractId?: string;
    /** 关联合同编号 */
    contractNumber?: string;
    /** 关联合同名称 */
    contractName?: string;
    /** 关联合同类型 */
    contractType?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 销售人员姓名 */
    salePerson?: string;
    /** 申请时间 */
    applicationTime?: string;
    /** 价税合计 */
    ticketAmount?: number;
    /** 价税合计(全票) */
    ticketAmountAll?: number;
    /** 开票时间 */
    ticketTime?: string;
    /** 开票类型 */
    ticketType?: string;
    /** 票据类型 */
    billType?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 收款银行 */
    collectionBank?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 银行账户 */
    account?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 注册地址 */
    registeredAddress?: string;
    /** 开票内容 */
    ticketContent?: string;
    /** 规格型号 */
    model?: string;
    /** 单位 */
    unit?: string;
    /** 数量 */
    quantity?: string;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 税额（全票） */
    rateAmountAll?: number;
    /** 备注 */
    remark?: string;
    /** 状态 */
    status?: string;
    /** 操作状态 */
    operateStatus?: string;
    /** 销货单位名称 */
    salesFirm?: string;
    /** 销货纳税人识别号 */
    salesTaxpayerNum?: string;
    /** 地址、电话 */
    salesContact?: string;
    /** 开户行及账号 */
    salesAccount?: string;
    /** 开票人 */
    drawerName?: string;
    /** 开票人id */
    drawerId?: string;
    /** 开票金额 */
    ticketAndRate?: number;
    /** 开票金额（全票） */
    ticketAndRateAll?: number;
    /** 开票明细 */
    invoiceNumberList?: InvoiceNumberDTO[];
  };

  type IPageFinalDetailsResp = {
    total?: number;
    pages?: number;
    records?: FinalDetailsResp[];
    current?: number;
    size?: number;
  };

  type Leader = {
    id?: string;
    username?: string;
  };

  type leaveAppInfoParams = {
    req: IdReq;
  };

  type LeaveAppInfoResp = {
    /** id */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 休假类型 */
    leaveType?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 部门 */
    department?: string;
    /** 申请前剩余天数 */
    leaveBalance?: number;
    /** 开始日期 */
    fromDate?: string;
    /** 结束日期 */
    toDate?: string;
    /** 总休假天数 */
    totalLeaveDays?: number;
    /** 实际休假天数 */
    actualLeaveDays?: number;
    /** 原因 */
    description?: string;
    /** 半天日期 */
    halfDate?: string;
    /** 流程状态 */
    activiStatus?: string;
  };

  type LeaveAppInsertReq = {
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 休假类型 */
    leaveType?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 部门 */
    department?: string;
    /** 开始日期 */
    fromDate?: string;
    /** 结束日期 */
    toDate?: string;
    /** 总休假天数 */
    totalLeaveDays?: number;
    /** 剩余天数 */
    leaveBalance?: number;
    /** 原因 */
    description?: string;
    /** 半天日期 */
    halfDate?: string;
  };

  type LeaveAppPageResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 部门 */
    department?: string;
    /** 开始日期 */
    fromDate?: string;
    /** 结束日期 */
    toDate?: string;
    /** 流程状态 */
    activiStatus?: string;
    /** 总休假天数 */
    totalLeaveDays?: number;
  };

  type LeaveAppUpdateReq = {
    /** ID */
    id?: string;
    /** 员工编号 */
    employee?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 休假类型 */
    leaveType?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 部门 */
    department?: string;
    /** 开始日期 */
    fromDate?: string;
    /** 结束日期 */
    toDate?: string;
    /** 总休假天数 */
    totalLeaveDays?: number;
    /** 剩余天数 */
    leaveBalance?: number;
    /** 原因 */
    description?: string;
    /** 半天日期 */
    halfDate?: string;
  };

  type LeaveBalanceListResp = {
    /** ID */
    id?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 剩余天数 */
    leaveBalance?: number;
  };

  type LeavePageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortTotalLeaveDays?: string;
  };

  type LeavePolicyDetailInfoResp = {
    /** ID */
    id?: string;
    /** 休假类型id */
    leaveTypeId?: string;
    /** 类型名称 */
    leaveTypeName?: string;
    /** 年度配额 */
    annualAllocation?: number;
  };

  type LeavePolicyDetailInsertReq = {
    /** 休假类型ID */
    leaveTypeId: string;
    /** 类型名称 */
    leaveTypeName: string;
    /** 年度配额 */
    annualAllocation: number;
  };

  type LeavePolicyDetailUpdateReq = {
    /** ID */
    id: string;
    leaveTypeId?: string;
    leaveTypeName?: string;
    /** 年度配额 */
    annualAllocation: number;
  };

  type LeavePolicyInfoResp = {
    /** 编号 */
    documentNumber?: string;
    /** 详情列表 */
    details?: LeavePolicyDetailInfoResp[];
  };

  type LeavePolicyInsertReq = {
    /** 休假政策信息 */
    leavePolicyDetails?: LeavePolicyDetailInsertReq[];
  };

  type LeavePolicyPageResp = {
    /** ID */
    id?: string;
    /** 编号 */
    documentNumber?: string;
  };

  type leaveSellInfoParams = {
    req: IdReq;
  };

  type LeaveSellInfoResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 休假申请ID */
    leaveAppId?: string;
    /** 休假申请编号 */
    leaveAppNumber?: string;
    /** 销假时间 */
    sellDate?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 休假类型 */
    leaveType?: string;
    /** 是否半天 */
    half?: number;
    /** 总休假天数 */
    totalLeaveDays?: number;
    /** 开始日期 */
    fromDate?: string;
    /** 结束日期 */
    toDate?: string;
    /** 用户名称 */
    username?: string;
  };

  type LeaveSellInsertReq = {
    /** 休假申请ID */
    leaveAppId?: string;
    /** 休假申请编号 */
    leaveAppNumber?: string;
    /** 销假时间 */
    sellDate?: string;
    /** 是否半天 0不是 1是 */
    half?: number;
  };

  type LeaveSellUpdateReq = {
    id?: string;
    /** 休假申请ID */
    leaveAppId?: string;
    /** 休假申请编号 */
    leaveAppNumber?: string;
    /** 销假时间 */
    sellDate?: string;
  };

  type LeaveTypeInsertReq = {
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 允许最大分配天数 */
    maxLeavesAllowed?: number;
    /** 申请休假前最少工作天数 */
    applicableAfter?: number;
    /** 单次最长连续休假天数 */
    maxContinuousDaysAllowed?: number;
    /** 是否带薪休假;0不是1是 */
    ifPpl?: string;
    /** 休假时工资份额 */
    wageShare?: number;
  };

  type LeaveTypeListResp = {
    /** ID */
    id?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
  };

  type LeaveTypePageResp = {
    /** ID */
    id?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 允许最大分配天数 */
    maxLeavesAllowed?: number;
    /** 单次最长连续休假天数 */
    maxContinuousDaysAllowed?: number;
    /** 是否带薪休假 */
    ifPpl?: string;
    /** 休假时工资份额 */
    wageShare?: number;
    /** 申请休假前最少工作天数 */
    applicableAfter?: number;
  };

  type LeaveTypeUpdateReq = {
    /** ID */
    id?: string;
    /** 休假类型名称 */
    leaveTypeName?: string;
    /** 允许最大分配天数 */
    maxLeavesAllowed?: number;
    /** 申请休假前最少工作天数 */
    applicableAfter?: number;
    /** 单次最长连续休假天数 */
    maxContinuousDaysAllowed?: number;
    /** 是否带薪休假;0不是1是 */
    ifPpl?: string;
    /** 休假时工资份额 */
    wageShare?: number;
  };

  type LogoffListReq = {
    payNumber?: string;
    payMoney?: string;
    institutionName?: string;
    refundTag?: string;
    sortPayMoney?: string;
    sortPayTime?: string;
    sortExpectedReturnTime?: string;
    sortWriteOffsTime?: string;
    applicationUser?: string;
  };

  type LogoffReq = {
    /** 付款id */
    payId?: string;
    /** 退款ids */
    refundIds?: string[];
  };

  type MainContractDifferenceResp = {
    /** id */
    id?: string;
    /** 金额 */
    contractAmount?: number;
    /** 编号 */
    contractNumber?: string;
    /** 名称 */
    contractName?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 与采购合同总差额 */
    purDifference?: number;
    /** 与采购合同总差额比 */
    purDifferenceRatio?: number;
    /** 关联的采购合同 */
    purContractList?: PurContractDifferenceResp[];
  };

  type MainContractExtraReq = {
    /** ID */
    id?: string;
    /** 合同类别 */
    contractCategory?: string;
  };

  type MainContractExtraResp = {
    /** 计划详细信息表 */
    payPlanDetailInfoList?: PayPlanDetailInfoResp[];
    /** 对应主合同的收款信息 */
    collectionPlanList?: CollectionPlanResp[];
    /** 其他采购合同计划详细信息表 */
    otherPayPlanDetailInfoList?: PayPlanDetailInfoResp[];
    /** 付款明细 */
    paymentList?: PaymentExtraResp[];
  };

  type MainContractInfoReq = {
    /** 主键 */
    id?: string;
    /** 合同类别 */
    contractCategory?: string;
    /** 框架合同 */
    isFrameworkAgreement?: number;
    /** 服务类别 */
    serveCategory?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同性质 */
    contractQuality?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 备注 */
    remark?: string;
    /** 内容概述 */
    overview?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 客户联系人 */
    fpContact?: string;
    /** 客户联系方式 */
    fpContactWay?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 商务助理 */
    businessAssistantName?: string;
    /** 商务助理主键 */
    businessAssistantId?: string;
    /** 最终用户 */
    endUser?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 行业 */
    industry?: string;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 售前项目主键 */
    projectId?: string;
    /** 售前项目编号 */
    projectNumber?: string;
    /** 售前项目名称 */
    projectName?: string;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
  };

  type MainContractInfoResp = {
    /** 主键 */
    id?: string;
    /** 合同类别 */
    contractCategory?: string;
    /** 合同编号 */
    contractNumber?: string;
    isFrameworkAgreement?: number;
    /** 服务类别 */
    serveCategory?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同性质 */
    contractQuality?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 备注 */
    remark?: string;
    /** 内容概述 */
    overview?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 客户联系人 */
    fpContact?: string;
    /** 客户联系方式 */
    fpContactWay?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 商务助理 */
    businessAssistantName?: string;
    /** 商务助理主键 */
    businessAssistantId?: string;
    /** 最终用户 */
    endUser?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 行业 */
    industry?: string;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 售前成本 */
    preSalesCost?: number;
    /** 差额 */
    difference?: number;
    /** 差额比 */
    differenceRatio?: number;
    /** 垫资金额 */
    advancesAmount?: number;
    /** 项目列表 */
    proInfoList?: BrieflyProInfo[];
  };

  type MainContractListResp = {
    /** 主键 */
    id?: string;
    /** 合同类别 */
    contractCategory?: string;
    /** 合同编号 */
    contractNumber?: string;
    isFrameworkAgreement?: number;
    /** 服务类别 */
    serveCategory?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同性质 */
    contractQuality?: string;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 备注 */
    remark?: string;
    /** 内容概述 */
    overview?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 客户联系人 */
    fpContact?: string;
    /** 客户联系方式 */
    fpContactWay?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 最终用户 */
    endUser?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 行业 */
    industry?: string;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 售前项目主键 */
    projectId?: string;
    /** 售前项目编号 */
    projectNumber?: string;
    /** 售前项目名称 */
    projectName?: string;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 销售项目编号 */
    xsProjectNumber?: BrieflyProInfo[];
    /** 售后项目编号 */
    shProjectNumber?: BrieflyProInfo[];
  };

  type MainContractReq = {
    mainContractInfo?: MainContractInfoReq;
    ticketInfo?: TicketInfoReq;
    collectionBankInfo?: CollectionBankReq;
    /** 合同内容请求参数集合 */
    contractContentList?: ContractContentReq[];
    /** 项目经理请求参数集合 */
    proMangerList?: ProMangerReq[];
    /** 收款计划请求参数对象 */
    collectionPlanList?: CollectionPlanReq[];
  };

  type MainContractResp = {
    mainContractInfo?: MainContractInfoResp;
    ticketInfo?: TicketInfoResp;
    collectionBankInfo?: CollectionBankResp;
    /** 合同内容响应对象 */
    contractContentList?: ContractContentResp[];
    /** 项目经理响应对象集合 */
    proMangerList?: ProMangerResp[];
    collectionInfo?: CollectionInfoResp;
    /** 收款计划响应对象集合 */
    collectionPlanList?: CollectionPlanResp[];
    /** 开票记录表 */
    contractInvoicingList?: InvoicingResp[];
    /** 采购合同信息 */
    purConDetailList?: PurConDetailResp[];
  };

  type MainCostEditReq = {
    /** 主合同主键 */
    id?: string;
    /** 资金成本 */
    fundCost: number;
    /** 费用成本 */
    feesCost: number;
  };

  type Member = {
    /** 员工id */
    id?: string;
    /** 是否同步 */
    isSync?: boolean;
  };

  type MobileFlowReq = {
    /** 流程类型 */
    type?: string;
    /** 页数 */
    pageNum?: number;
    /** 每页多少条 */
    pageSize?: number;
    /** 编号模糊查询 */
    searchNumber?: string;
    /** 申请人模糊查询 */
    searchStartUsername?: string;
    /** 业务状态 */
    approvalStatus?: string;
  };

  type NameReq = {
    name?: string;
  };

  type NextWeek = {
    /** 星期 */
    recordWeek?: string;
    /** 日期 */
    recordDate?: string;
    /** 工作计划 */
    workPlan?: string;
    /** 项目 */
    project?: string;
    /** 项目名称 */
    projectName?: string;
    /** 工作类型 */
    workType?: string;
    /** 备注 */
    remark?: string;
  };

  type NoticeUserReq = {
    /** 制度主键 */
    rulesId?: string;
    /** 通知用户主键 */
    userIdList?: string[];
  };

  type NoticeUserResp = {
    /** 主键 */
    id?: string;
    /** 用户主键 */
    userId?: string;
    /** 用户名称 */
    userName?: string;
    /** 制度主键 */
    rulesId?: string;
    /** 是否发送邮件 */
    sendEmail?: boolean;
    /** 发送邮件时间 */
    sendTime?: string;
  };

  type OperationRecord = {
    id?: string;
    businessId?: string;
    username?: string;
    action?: string;
    content?: string;
    createdDate?: string;
  };

  type PageAnnouncementPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: AnnouncementPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageAptitudeResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: AptitudeResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageAttRecordListResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: AttRecordListResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageCertificateResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: CertificateResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageCollectTicketPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: CollectTicketPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageContractPayResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: ContractPayResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageContractProfitResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: ContractProfitResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageCoopAgreeResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: CoopAgreeResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageFinaPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: FinaPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageFirmResourceInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: FirmResourceInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageHolidayDetailsPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: HolidayDetailsPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageHolidayPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: HolidayPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageInitialPerformanceAppraisalTableResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: InitialPerformanceAppraisalTableResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageInnerConInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: InnerConInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageInstitutionListResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: InstitutionListResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageInvoicingResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: InvoicingResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageLeaveAppPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: LeaveAppPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageLeavePolicyPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: LeavePolicyPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageLeaveSellInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: LeaveSellInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageLeaveTypePageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: LeaveTypePageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageMainContractInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: MainContractInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePartnerInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PartnerInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePaymentApplicationPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PaymentApplicationPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePaymentAppResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PaymentAppResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePendingInvoiceResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PendingInvoiceResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePendingReceiptContractResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PendingReceiptContractResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePendingReceiptProjectResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PendingReceiptProjectResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageProjectDeliveryResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: ProjectDeliveryResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PagePurContractInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PurContractInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageReceiptsResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: ReceiptsResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageRecruitInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: RecruitInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
  };

  type PageRoleInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: RoleInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageRulesResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: RulesResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageTalentInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: TalentInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageTaskPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: TaskPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageTenderResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: TenderResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageTrainReimbursementResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: TrainReimbursementResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageUserPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: UserPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageUserSalaryResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: UserSalaryResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageUserScheduleResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: UserScheduleResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageWeekPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: WeekPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PageWorkOrderPageResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: WorkOrderPageResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
  };

  type PartnerInfoReq = {
    /** 主键 */
    id?: string;
    /** 业务伙伴类别 */
    partnerType?: string;
    /** 客户名称 */
    clientName?: string;
    /** 客户简称 */
    clientAbbreviation?: string;
    /** 客户编号 */
    clientNumber?: string;
    /** 客户经理 */
    clientManger?: string;
    /** 客户经理主键 */
    clientMangerId?: string;
    /** 所在省 */
    province?: string;
    /** 所在区域 */
    area?: string;
    /** 创建日期 */
    clientCreateTime?: string;
    /** 所在城市 */
    city?: string;
    /** 合作方式 */
    cooperateWay?: string;
    /** 行业 */
    industry?: string;
    /** 合作方 */
    agreement?: string;
    /** 客户联系人 */
    firstContact?: string;
    /** 客户联系人方式 */
    firstContactWay?: string;
    /** 客户联系人职务 */
    firstPosition?: string;
    /** 客户地址 */
    firstAddress?: string;
    /** 合作方联系人 */
    secondContact?: string;
    /** 合作方联系人联系方式 */
    secondContactWay?: string;
    /** 合作方联系人职务 */
    secondPosition?: string;
    /** 合作方地址 */
    secondAddress?: string;
    /** 客户经理集合 */
    clientMangers?: ClientMangers[];
    /** 级别 */
    grade?: number;
    /** 是否创建售前项目 */
    hasCrProject?: string;
    /** 售前项目编号 */
    projectName?: string;
  };

  type PartnerInfoResp = {
    /** 主键 */
    id?: string;
    /** 业务伙伴类别 */
    partnerType?: string;
    /** 客户名称 */
    clientName?: string;
    /** 客户简称 */
    clientAbbreviation?: string;
    /** 编号规则 */
    serialNumberRule?: string;
    /** 客户编号 */
    clientNumber?: string;
    /** 客户经理 */
    clientManger?: string;
    /** 客户经理主键 */
    clientMangerId?: string;
    /** 所在省 */
    province?: string;
    /** 所在区域 */
    area?: string;
    /** 创建日期 */
    clientCreateTime?: string;
    /** 所在城市 */
    city?: string;
    /** 合作方式 */
    cooperateWay?: string;
    /** 行业 */
    industry?: string;
    /** 合作方 */
    agreement?: string;
    /** 客户联系人 */
    firstContact?: string;
    /** 客户联系人联系方式 */
    firstContactWay?: string;
    /** 客户联系人职务 */
    firstPosition?: string;
    /** 客户地址 */
    firstAddress?: string;
    /** 合作方联系人 */
    secondContact?: string;
    /** 合作方联系人联系方式 */
    secondContactWay?: string;
    /** 合作方联系人职务 */
    secondPosition?: string;
    /** 合作方地址 */
    secondAddress?: string;
    /** 客户经理集合 */
    clientMangers?: ClientMangers[];
    /** 流程审批状态 */
    activiStatus?: string;
    /** 机构名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 级别 */
    grade?: number;
    /** 是否创建售前项目 */
    hasCrProject?: string;
    /** 售前项目编号 */
    projectName?: string;
  };

  type PartnerReq = {
    partnerInfo?: PartnerInfoReq;
    /** 销售请求对象集合 */
    salePersonList?: SalePersonReq[];
    /** 销售计划请求参数 */
    salePlanList?: SalePlanReq[];
  };

  type PartnerResp = {
    partnerInfo?: PartnerInfoResp;
    /** 业务伙伴关联销售响应对象集合 */
    salePersonList?: SalePersonResp[];
    /** 销售计划信息 */
    salePlanList?: SalePlanResp[];
    /** 执行合同集合 */
    executeContractList?: ExecuteContractResp[];
    /** 项目简要信息 */
    brieflyProInfoList?: BrieflyProInfo[];
  };

  type PayInfoReq = {
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 备注 */
    remark?: string;
  };

  type PayInfoResp = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 单位银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 备注 */
    remark?: string;
  };

  type Payment = {
    id?: string;
    period?: number;
    contractId?: string;
    contractType?: string;
    contractNumber?: string;
    payPlanId?: string;
    serialNumberRule?: string;
    payNumber?: string;
    payCondition?: string;
    payWay?: string;
    applicationTime?: string;
    estimatePayTime?: string;
    estimatePayAmount?: number;
    payTime?: string;
    payAmount?: number;
    institutionName?: string;
    bank?: string;
    account?: string;
    remark?: string;
    useWay?: string;
    applicationUser?: string;
    applicationUserId?: string;
    department?: string;
    approvalStatus?: string;
    fundingCategory?: string;
    paymentType?: string;
    countInvoicedTime?: string;
    receiptAmount?: number;
    payStatus?: string;
    status?: string;
    deleteIf?: number;
    operateStatus?: string;
    claimStatus?: string;
    activiStatus?: string;
    paymentAppId?: string;
    paymentAppNumber?: string;
    writeOffsTag?: string;
    writeOffsTime?: string;
    createdBy?: string;
    createdTime?: string;
    updatedBy?: string;
    updatedTime?: string;
    cancelDesc?: string;
  };

  type paymentApplicationDropDownListParams = {
    institutionName: string;
  };

  type paymentApplicationInfoParams = {
    req: IdReq;
  };

  type PaymentApplicationInfoResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 申请人ID */
    userId?: string;
    /** 申请人姓名 */
    username?: string;
    /** 所属部门 */
    departmentName?: string;
    /** 申请日期 */
    applicationDate?: string;
    /** 客户id */
    customerId?: string;
    /** 客户名称 */
    customerName?: string;
    /** 归集项目 */
    collectionProject?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 对应网址 */
    correspondingUrl?: string;
    /** 付款方式 */
    paymentMethod?: string;
    /** 账户名 */
    accountName?: string;
    /** 账户 */
    account?: string;
    /** 开户行 */
    openingBank?: string;
    /** 付款金额 */
    paymentAmount?: string;
    /** 用途 */
    purpose?: string;
    /** 资金类别 */
    fundCategory?: string;
    /** 预计退回时间 */
    expectedReturnTime?: string;
    /** 预计付款日期 */
    expectedPaymentDate?: string;
    /** 付款截至时间 */
    paymentDeadline?: string;
    /** 备注 */
    remarks?: string;
    /** 说明 */
    directions?: string;
    /** 发票 */
    invoice?: string;
    /** 付款状态 */
    payStatus?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 付款类型 */
    paymentType?: string;
    /** 是否开票 0 不开 1 开 */
    hasInvoice?: string;
    /** 发票ID */
    invoiceId?: string;
    /** 发票状态 */
    invoiceStatus?: string;
  };

  type PaymentApplicationInsertReq = {
    /** 申请人ID */
    userId?: string;
    /** 申请人姓名 */
    username?: string;
    /** 所属部门 */
    departmentName?: string;
    /** 申请日期 */
    applicationDate?: string;
    /** 客户id */
    customerId?: string;
    /** 客户名称 */
    customerName?: string;
    /** 归集项目 */
    collectionProject?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 对应网址 */
    correspondingUrl?: string;
    /** 付款方式 */
    paymentMethod?: string;
    /** 账户名 */
    accountName?: string;
    /** 账户 */
    account?: string;
    /** 开户行 */
    openingBank?: string;
    /** 付款金额 */
    paymentAmount?: string;
    /** 用途 */
    purpose?: string;
    /** 资金类别 */
    fundCategory?: string;
    /** 预计退回时间 */
    expectedReturnTime?: string;
    /** 预计付款日期 */
    expectedPaymentDate?: string;
    /** 付款截至时间 */
    paymentDeadline?: string;
    /** 备注 */
    remarks?: string;
    /** 说明 */
    directions?: string;
    /** 发票 */
    invoice?: string;
    /** 是否开票 0 不开 1 开 */
    hasInvoice?: string;
    /** 发票ID */
    invoiceId?: string;
    /** 发票状态 */
    invoiceStatus?: string;
    /** 付款类型 */
    paymentType?: string;
  };

  type PaymentApplicationLogoffListResp = {
    /** ID */
    id?: string;
    /** 保证金编号 */
    bondNumber?: string;
    /** 保证金id */
    bondId?: string;
    /** 支付编号 */
    payNumber?: string;
    /** 支付时间 */
    payTime?: string;
    /** 支付金额 */
    payMoney?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账号 */
    bankAcNo?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 部门 */
    department?: string;
    /** 申请人 */
    applicationUser?: string;
    /** 项目名称 */
    projectName?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 预计退回时间 */
    expectedReturnTime?: string;
    /** 退款(销账)日期 */
    writeOffsTime?: string;
    /** 销账状态 */
    refundTag?: string;
  };

  type PaymentApplicationLogoffResp = {
    /** 销账列表 */
    paymentAppList?: PaymentApplicationLogoffListResp[];
    /** 待销账金额 */
    logoffAmountCount?: number;
  };

  type PaymentApplicationPageResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 流程状态 */
    activiStatus?: string;
    /** 支付类型 */
    paymentType?: string;
    /** 支付方式 */
    paymentMethod?: string;
    /** 付款金额 */
    paymentAmount?: string;
    /** 申请人姓名 */
    username?: string;
    /** 申请日期 */
    applicationDate?: string;
    /** 项目名称 */
    projectName?: string;
    /** 付款状态 */
    payStatus?: string;
  };

  type PaymentApplicationUpdateReq = {
    /** ID */
    id?: string;
    /** 申请人ID */
    userId?: string;
    /** 申请人姓名 */
    username?: string;
    /** 所属部门 */
    departmentName?: string;
    /** 申请日期 */
    applicationDate?: string;
    /** 客户id */
    customerId?: string;
    /** 客户名称 */
    customerName?: string;
    /** 归集项目 */
    collectionProject?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 对应网址 */
    correspondingUrl?: string;
    /** 付款方式 */
    paymentMethod?: string;
    /** 账户名 */
    accountName?: string;
    /** 账户 */
    account?: string;
    /** 开户行 */
    openingBank?: string;
    /** 付款金额 */
    paymentAmount?: string;
    /** 用途 */
    purpose?: string;
    /** 资金类别 */
    fundCategory?: string;
    /** 预计退回时间 */
    expectedReturnTime?: string;
    /** 预计付款日期 */
    expectedPaymentDate?: string;
    /** 付款截至时间 */
    paymentDeadline?: string;
    /** 备注 */
    remarks?: string;
    /** 说明 */
    directions?: string;
    /** 发票 */
    invoice?: string;
    /** 是否开票 0 不开 1 开 */
    hasInvoice?: string;
    /** 发票ID */
    invoiceId?: string;
    /** 发票状态 */
    invoiceStatus?: string;
    /** 付款类型 */
    paymentType?: string;
  };

  type PaymentAppReq = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同支付计划主键 */
    payPlanId?: string;
    /** 付款流水主键 */
    paymentId?: string;
    /** 申请流水号 */
    applicationNumber?: string;
    /** 付款条件 */
    payCondition?: string;
    /** 付款方式 */
    payWay?: string;
    /** 申请时间 */
    applicationTime?: string;
    /** 计划付款时间 */
    estimatePayTime?: string;
    /** 计划付款金额 */
    estimatePayAmount?: number;
    /** 实际付款时间 */
    payTime?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 账户 */
    account?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 申请人 */
    applicationUser?: string;
    /** 申请人员主键 */
    applicationUserId?: string;
    /** 部门 */
    department?: string;
    /** 审批状态 */
    approvalStatus?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 支付类型 */
    paymentType?: string;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 付款状态 */
    payStatus?: string;
    /** 状态 */
    status?: string;
    /** 操作状态 */
    operateStatus?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type PaymentAppResp = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 甲方名称 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 合同支付计划主键 */
    payPlanId?: string;
    /** 付款流水主键 */
    paymentId?: string;
    /** 申请流水号 */
    applicationNumber?: string;
    /** 付款条件 */
    payCondition?: string;
    /** 付款方式 */
    payWay?: string;
    /** 申请时间 */
    applicationTime?: string;
    /** 计划付款时间 */
    estimatePayTime?: string;
    /** 计划付款金额 */
    estimatePayAmount?: number;
    /** 实际付款时间 */
    payTime?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 账户 */
    account?: string;
    /** 纳税人识别号 */
    taxpayerNumber?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 申请人员姓名 */
    applicationUser?: string;
    /** 申请人员主键 */
    applicationUserId?: string;
    /** 部门 */
    department?: string;
    /** 审批状态 */
    approvalStatus?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 支付类型 */
    paymentType?: string;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 待收票金额 */
    awaitReAmount?: number;
    /** 付款状态 */
    payStatus?: string;
    /** 状态 */
    status?: string;
    /** 操作状态 */
    operateStatus?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type PaymentExtraResp = {
    id?: string;
    period?: number;
    contractId?: string;
    contractType?: string;
    contractNumber?: string;
    payPlanId?: string;
    projectId?: string;
    projectNumber?: string;
    serialNumberRule?: string;
    payNumber?: string;
    payCondition?: string;
    payWay?: string;
    applicationTime?: string;
    estimatePayTime?: string;
    estimatePayAmount?: number;
    payTime?: string;
    payAmount?: number;
    institutionName?: string;
    bank?: string;
    account?: string;
    remark?: string;
    useWay?: string;
    applicationUser?: string;
    applicationUserId?: string;
    department?: string;
    approvalStatus?: string;
    fundingCategory?: string;
    paymentType?: string;
    countInvoicedTime?: string;
    receiptAmount?: number;
    payStatus?: string;
    status?: string;
    deleteIf?: number;
    operateStatus?: string;
    claimStatus?: string;
    activiStatus?: string;
    paymentAppId?: string;
    paymentAppNumber?: string;
    writeOffsTag?: string;
    writeOffsTime?: string;
    createdBy?: string;
    createdTime?: string;
    updatedBy?: string;
    updatedTime?: string;
    cancelDesc?: string;
  };

  type PaymentFeesResp = {
    /** 付款主键 */
    id?: string;
    /** 项目主键 */
    projectId?: string;
    /** 付款流水号 */
    payNumber?: string;
    /** 付款方式 */
    payWay?: string;
    /** 实际付款金额 */
    paymentAmount?: string;
    /** 成本 */
    cost?: number;
    /** 备注 */
    remarks?: string;
    /** 申请人 */
    username?: string;
    /** 申请人员主键 */
    userId?: string;
    /** 部门 */
    department?: string;
    employeeDepartment?: string;
    /** 资金类别 */
    fundCategory?: string;
    /** 支付类型 */
    paymentType?: string;
    /** 销账标志 */
    writeOffsTag?: string;
    /** 成本状态 0：未关联 1：当前合同关联 2：其他合同关联 */
    costStatus?: string;
  };

  type PaymentPagePaymentResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PaymentResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
    /** 待付款金额总计 */
    amountToBePaid?: number;
  };

  type PaymentPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortPayAmount?: string;
    sortEstimatePayTime?: string;
    sortPayTime?: string;
  };

  type PaymentReq = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同支付计划主键 */
    payPlanId?: string;
    /** 付款流水号 */
    payNumber?: string;
    /** 付款条件 */
    payCondition?: string;
    /** 付款方式 */
    payWay?: string;
    /** 申请时间 */
    applicationTime?: string;
    /** 计划付款时间 */
    estimatePayTime?: string;
    /** 计划付款金额 */
    estimatePayAmount?: number;
    /** 实际付款时间 */
    payTime?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 申请人 */
    applicationUser?: string;
    /** 申请人员主键 */
    applicationUserId?: string;
    /** 部门 */
    department?: string;
    /** 自定义审批状态 */
    approvalStatus?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 支付类型 */
    paymentType?: string;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 付款状态 */
    payStatus?: 'NOT_PAY' | 'PAY';
    /** 状态 */
    status?: string;
    /** 操作状态 */
    operateStatus?: string;
    /** 流程审批状态 */
    activiStatus?: string;
    /** 支付申请主键 */
    paymentAppId?: string;
    /** 支付申请编号 */
    paymentAppNumber?: string;
    /** 销账标志 */
    writeOffsTag?: string;
  };

  type PaymentResp = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同支付计划主键 */
    payPlanId?: string;
    /** 付款流水号 */
    payNumber?: string;
    /** 付款条件 */
    payCondition?: string;
    /** 付款方式 */
    payWay?: string;
    /** 申请时间 */
    applicationTime?: string;
    /** 计划付款时间 */
    estimatePayTime?: string;
    /** 计划付款金额 */
    estimatePayAmount?: number;
    /** 实际付款时间 */
    payTime?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 申请人 */
    applicationUser?: string;
    /** 申请人员主键 */
    applicationUserId?: string;
    /** 部门 */
    department?: string;
    /** 自定义审批状态 */
    approvalStatus?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 支付类型 */
    paymentType?: string;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 付款状态 */
    payStatus?: string;
    /** 状态 */
    status?: string;
    /** 操作状态 */
    operateStatus?: string;
    /** 流程审批状态 */
    activiStatus?: string;
    /** 支付申请主键 */
    paymentAppId?: string;
    /** 支付申请编号 */
    paymentAppNumber?: string;
    /** 销账标志 */
    writeOffsTag?: string;
    /** 取消描述 */
    cancelDesc?: string;
  };

  type PayPlanDetailInfoReq = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同类型 */
    contractType?: string;
    /** 预计付款时间 */
    estimatePayTime?: string;
    /** 预计付款金额 */
    estimatePayAmount?: number;
    /** 付款条件 */
    payCondition?: string;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 不含税金额 */
    excludeRaAmount?: number;
    /** 付款方式 */
    payWay?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 支付申请编号 */
    paymentAppNumber?: string;
    /** 支付申请主键 */
    paymentAppId?: string;
    /** 付款流水号 */
    payNumber?: string;
    /** 付款流水主键 */
    paymentId?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 实际付款时间 */
    payTime?: string;
    /** 备注 */
    remark?: string;
    /** 付款状态 */
    payStatus?: string;
    /** 状态 */
    status?: string;
    /** 审批状态 */
    approvalStatus?: string;
    /** 操作状态 */
    operateStatus?: string;
  };

  type PayPlanDetailInfoResp = {
    /** 主键 */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 预计付款时间 */
    estimatePayTime?: string;
    /** 预计付款金额 */
    estimatePayAmount?: number;
    /** 付款条件 */
    payCondition?: string;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 不含税金额 */
    excludeRaAmount?: number;
    /** 付款方式 */
    payWay?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 支付申请编号 */
    paymentAppNumber?: string;
    /** 支付申请主键 */
    paymentAppId?: string;
    /** 付款流水号 */
    payNumber?: string;
    /** 付款流水主键 */
    paymentId?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 实际付款时间 */
    payTime?: string;
    /** 备注 */
    remark?: string;
    /** 付款状态 */
    payStatus?: string;
    /** 付款计划状态 */
    payPlanStatus?: string;
    /** 状态 */
    status?: string;
    /** 审批状态 */
    approvalStatus?: string;
    /** 操作状态 */
    operateStatus?: string;
  };

  type PaySummaryResp = {
    /** 合同总金额 */
    contractAmount?: number;
    /** 已收票金额 */
    receivedAmount?: number;
    /** 待收票金额 */
    awaitReAmount?: number;
    /** 收票总金额 */
    receiveTotalAmount?: number;
    /** 已付款金额 */
    payedAmount?: number;
    /** 待付款金额 */
    awaitPayAmount?: number;
    /** 总税额 */
    totalTax?: number;
    /** 当前税额 */
    currentTax?: number;
  };

  type PendingInvoiceResp = {
    id?: string;
    contractId?: string;
    contractName?: string;
    contractNumber?: string;
    period?: number;
    estimateReAmount?: number;
    estimateReTime?: string;
    status?: string;
    ticketAmount?: number;
    ticketTime?: string;
    invoicingNumber?: string;
    receiveAmount?: number;
    receiveTime?: string;
    claimTime?: string;
    collectNumber?: string;
  };

  type PendingReceiptContractResp = {
    /** 合同ID */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 销售 */
    salesman?: string;
    /** 总金额 */
    totalAmount?: number;
    /** 已付款金额 */
    payedAmount?: number;
    /** 已收票金额 */
    receivedAmount?: number;
    /** 待收票金额 */
    awaitReceiptAmount?: number;
    /** 总税额 */
    totalTax?: number;
    /** 当前税额 */
    currentTax?: number;
    /** 更新日期 */
    updateDate?: string;
  };

  type PendingReceiptProjectResp = {
    /** 项目付款申请ID */
    id?: string;
    /** 项目付款申请编号 */
    documentNumber?: string;
    /** 项目id */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 申请人ID */
    userId?: string;
    /** 申请人姓名 */
    username?: string;
    /** 所属部门 */
    departmentName?: string;
    /** 申请日期 */
    applicationDate?: string;
    /** 客户名称 */
    customerName?: string;
    /** 付款金额 */
    paymentAmount?: string;
    /** 付款状态 */
    payStatus?: string;
  };

  type PerformanceConfirmReq = {
    /** ID */
    id: string;
    /** 年份 */
    receiveYear: string;
    /** 核算状态 */
    performanceStatus: 'ACCOUNT_REJECTED' | 'ACCOUNT_CONFIRMED';
    performanceId?: string;
  };

  type PerformanceConfReq = {
    /** ID */
    id: string;
    /** 年份 */
    receiveYear: string;
    performanceInfo?: PerformanceInfoReq;
    /** 售前项目报销ids */
    perfProjectsSq?: string[];
    /** 销售项目报销ids */
    perfProjectsXs?: string[];
    /** 售后项目报销ids */
    perfProjectsSh?: string[];
    /** 收款流水ids */
    perfReceipts?: string[];
    /** 付款流水ids */
    perfPayFlow?: string[];
    /** 项目付款ids */
    projectPays?: string[];
    performanceId?: string;
  };

  type PerformanceConfResp = {
    /** 售前项目 */
    perfProjectsSq?: PerfProjectResp[];
    /** 销售项目 */
    perfProjectsXs?: PerfProjectResp[];
    /** 售后项目 */
    perfProjectsSh?: PerfProjectResp[];
    /** 收款流水 */
    perfReceipts?: PerfReceiptsResp[];
    /** 已收款金额 */
    receivedAmountAll?: string;
    /** 付款流水 */
    perfPayFlow?: PerfPayFlowResp[];
    /** 已付款金额 */
    paidAmountAll?: string;
    perMainInfoResp?: PerMainInfoResp;
    /** 项目付款明细 */
    projectPayList?: ProjectPayResp[];
    performanceInfoResp?: PerformanceInfoResp;
  };

  type PerformanceIdReq = {
    /** ID */
    id: string;
    /** 年份 */
    receiveYear: string;
    performanceId?: string;
  };

  type PerformanceInfoReq = {
    /** 总比例 */
    ratio?: number;
    /** 销售提成比例 */
    salesList?: SalesRatioReq[];
    /** 咨询费用 */
    consultingFee?: number;
    /** 实施成本 */
    implementationCost?: number;
    /** 资金成本 */
    fundCost?: number;
    /** 备注 */
    remark?: string;
  };

  type PerformanceInfoResp = {
    /** 比例 */
    ratio?: number;
    /** 销售提成比例 */
    salesList?: SalesRatioResp[];
    /** 咨询费用 */
    consultingFee?: number;
    /** 实施成本 */
    implementationCost?: number;
    /** 资金成本 */
    fundCost?: number;
    /** 备注 */
    remark?: string;
  };

  type PerformancePagePerMainResultResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: PerMainResultResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
    /** 项目毛利 */
    projectGrossProfitCount?: number;
    /** 考核业绩 */
    performanceEvaluationCount?: number;
  };

  type PerfPayFlowResp = {
    /** 核算状态 */
    performanceStatus?: string;
    /** 当前核算 */
    currentPerformance?: boolean;
    /** 核算编号 */
    perfNumber?: string;
    /** 系统流水ID */
    id?: string;
    /** 期次 */
    period?: number;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同支付计划主键 */
    payPlanId?: string;
    /** 实际付款金额 */
    payAmount?: number;
    /** 对方开票日期 */
    countInvoicedTime?: string;
    /** 收票金额 */
    receiptAmount?: number;
    /** 付款状态 */
    payStatus?: string;
    /** 计划付款时间 */
    estimatePayTime?: string;
    /** 计划付款金额 */
    estimatePayAmount?: number;
    /** 税率 */
    rate?: number;
    /** 税额 */
    rateAmount?: number;
    /** 付款日期 */
    payTime?: string;
  };

  type PerfProjectDetailsResp = {
    /** 核算状态 */
    performanceStatus?: string;
    /** 当前核算 */
    currentPerformance?: boolean;
    /** 核算编号 */
    perfNumber?: string;
    /** 报销明细流水ID */
    id?: string;
    /** 项目ID */
    projectId?: string;
    /** 明细类型 */
    type?: string;
    /** 关联报销编号 */
    finaNumber?: string;
    /** 实际报销金额 */
    actualMoney?: string;
    /** 实际报销类型 */
    actualType?: string;
    /** 正当理由 */
    reason?: string;
    /** 备注 */
    remark?: string;
    /** 员工姓名 */
    username?: string;
    /** 报销日期 */
    finaDate?: string;
  };

  type PerfProjectResp = {
    /** 核算状态 */
    performanceStatus?: string;
    /** 当前核算 */
    currentPerformance?: boolean;
    /** 核算编号 */
    perfNumber?: string;
    /** 主键 */
    id?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目分类 */
    projectClassify?: string;
    /** 项目经理 */
    projectManger?: string;
    /** 项目经理ID */
    projectMangerId?: string;
    /** 项目创建时间 */
    proCreateTime?: string;
    /** 创建时间 */
    createdTime?: string;
    /** 项目状态 */
    status?: string;
    /** 项目开销 */
    spend?: number;
    /** 已执行工作量 */
    executedWorkload?: string;
    /** 成本估算 */
    costEstimate?: number;
    /** 预估工作量 */
    estimatedWorkload?: string;
    /** 项目报销明细 */
    perfProjectDetails?: PerfProjectDetailsResp[];
  };

  type PerfReceiptsResp = {
    /** 核算状态 */
    performanceStatus?: string;
    /** 当前核算 */
    currentPerformance?: boolean;
    /** 核算编号 */
    perfNumber?: string;
    /** 报销明细流水ID */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 收款编号 */
    collectNumber?: string;
    /** 收款时间 */
    receiveTime?: string;
    /** 收款方式 */
    collectionWay?: string;
    /** 收款类别 */
    collectionType?: string;
    /** 收款金额 */
    receiveAmount?: number;
    /** 期次 */
    period?: number;
    /** 计划收款时间 */
    estimateReTime?: string;
    /** 计划收款金额 */
    estimateReAmount?: number;
    /** 认领日期 */
    claimTime?: string;
    /** 税额 */
    rateAmount?: number;
    /** 税率 */
    rate?: number;
    /** 收款状态 */
    collectionStatus?: string;
  };

  type PerMainInfoResp = {
    /** 合同ID */
    id?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 核算编号 */
    documentNumber?: string;
    /** 年份 */
    receiveYear?: string;
    /** 核算状态 */
    performanceStatus?: string;
    /** 客户名称 */
    customerName?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 合同日期 */
    startTime?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 提交核算时间 */
    endTime?: string;
    /** 确认核算时间 */
    confirmTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type PerMainResultResp = {
    /** id */
    id?: string;
    rowNum?: string;
    /** 核算编号 */
    documentNumber?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同销售 */
    contractSalePerson?: string;
    /** 合同销售人员主键 */
    contractSalePersonId?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售主键 */
    salePersonId?: string;
    /** 核算状态 */
    performanceStatus?: string;
    /** 业绩表状态 */
    printStatus?: string;
    /** 客户名称 */
    customerName?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同日期 */
    startTime?: string;
    /** 主合同金额 */
    mainConAmount?: number;
    /** 比例 */
    ratio?: number;
    /** 咨询费用 */
    consultingFee?: number;
    /** 实施成本 */
    implementationCost?: number;
    /** 资金成本 */
    fundCost?: number;
    /** 备注 */
    remark?: string;
    /** 回款金额 */
    receiveAmount?: number;
    /** 缴税成本 */
    taxCost?: number;
    /** 采购成本 */
    purchaseCost?: number;
    /** 报销费用 */
    reimbursementFee?: number;
    /** 项目付款 */
    projectCost?: number;
    /** 项目毛利 */
    projectGrossProfit?: number;
    /** 考核业绩 */
    performanceEvaluation?: number;
  };

  type PositionInsertReq = {
    /** 职位名称 */
    positionName?: string;
    /** 所属部门id */
    departmentId?: string;
    /** 职级-id */
    positionLevel?: string;
  };

  type PositionListResp = {
    /** ID */
    id?: string;
    /** 职位名称 */
    positionName?: string;
    departmentId?: string;
    /** 职级 */
    positionLevel?: string;
  };

  type PositionUpdateReq = {
    /** ID */
    id?: string;
    /** 所属部门id */
    departmentId?: string;
    /** 职位名称 */
    positionName?: string;
    /** 职级 */
    positionLevel?: string;
  };

  type PrintInfoReq = {
    /** 年份 */
    receiveYear: string;
    /** 销售人员主键 */
    salePersonId: string;
  };

  type PrintInfoResp = {
    /** 销售 */
    salePerson?: string;
    /** 销售主键 */
    salePersonId?: string;
    /** 部门 */
    department?: string;
    /** 子部门 */
    departmentBranch?: string;
    /** 项目毛利 */
    projectGrossProfitCount?: number;
    /** 考核业绩 */
    performanceEvaluationCount?: number;
    /** 合计金额 */
    renderMoney?: number;
    /** 合同金额 */
    contractMoney?: number;
    /** 回款金额 */
    backMoney?: number;
    /** 咨询费用 */
    consultationMoney?: number;
    /** 缴税成本 */
    taxMoney?: number;
    /** 采购成本 */
    purchaseMoney?: number;
    /** 实施成本 */
    implementationMoney?: number;
    /** 资金成本 */
    capitalMoney?: number;
    /** 费用报销 */
    expenseMoney?: number;
    /** 项目付款 */
    projectMoney?: number;
    /** 业绩目标 */
    performanceTargets?: string;
    /** 业绩基线 */
    performanceBaseline?: string;
    /** 提成金额 */
    commissionAmount?: number;
    /** 业绩提交时间 */
    submitTime?: string;
    /** 业绩确认时间 */
    confirmTime?: string;
    /** 业绩状态 */
    performanceStatus?: string;
    /** 核算明细 */
    perMainResultRespList?: PerMainResultResp[];
  };

  type PrintSubmitReq = {
    /** 年份 */
    receiveYear: string;
    /** 销售人员主键 */
    salePersonId: string;
    /** 业绩目标 */
    performanceTargets: string;
    /** 业绩基线 */
    performanceBaseline: string;
    /** 提成金额 */
    commissionAmount: number;
  };

  type ProBaseInfoReq = {
    /** 主键 */
    id?: string;
    /** 项目分类 */
    projectClassify?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 项目创建时间 */
    proCreateTime?: string;
    /** 是否自动关闭 */
    autoClose?: boolean;
    /** 销售人员 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 项目经理 */
    projectManger?: string;
    /** 项目经理主键 */
    projectMangerId?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 项目周期 */
    projectCycle?: string;
    /** 预计工作量 */
    estimatedWorkload?: string;
    /** 项目奖金 */
    projectBonuses?: number;
    /** 项目绩效 */
    projectPerformance?: number;
    /** 客户名称 */
    clientName?: string;
    /** 客户主键 */
    clientId?: string;
    /** 客户联系人 */
    clientContact?: string;
    /** 客户联系方式 */
    clientContactWay?: string;
    /** 执行部门 */
    executeDepartment?: string;
    /** 项目地址 */
    projectAddress?: string;
    /** 状态 */
    status?: string;
    /** 成本估算 */
    costEstimate?: number;
    /** 目前开销 */
    spend?: number;
    /** 已执行工作量 */
    executedWorkload?: string;
    /** 项目进度 */
    projectProgress?: number;
    /** 技术部绩效 */
    techDepPerformance?: number;
    /** 项目概述 */
    description?: string;
    /** 审核状态 */
    activiStatus?: string;
    /** 设置全员可见 */
    allPersonAv?: boolean;
    /** 项目核算状态 */
    proPerformanceStatus?: string;
    /** 项目预算 SQ、SH、KF */
    projectBudget?: number;
    /** 售前成本 SH、KF */
    preCost?: number;
    /** 客户简称 */
    customerAbbreviation?: string;
  };

  type ProBaseInfoResp = {
    /** 主键 */
    id?: string;
    /** 项目分类 */
    projectClassify?: string;
    /** 合同编号（售后项目属性） */
    contractNumber?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同名称 */
    contractName?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目类型（售后项目属性） */
    projectType?: string;
    /** 创建时间 */
    proCreateTime?: string;
    /** 是否自动关闭 */
    autoClose?: boolean;
    /** 销售人员 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 项目经理 */
    projectManger?: string;
    /** 项目经理主键 */
    projectMangerId?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 项目周期（售后项目属性） */
    projectCycle?: string;
    /** 预计工作量（售后项目属性） */
    estimatedWorkload?: string;
    /** 项目奖金（售后项目属性） */
    projectBonuses?: number;
    /** 项目绩效（售后项目属性） */
    projectPerformance?: number;
    /** 客户名称 */
    clientName?: string;
    /** 项目经理主键 */
    clientId?: string;
    /** 客户联系人（售后项目属性） */
    clientContact?: string;
    /** 客户联系方式（售后项目属性） */
    clientContactWay?: string;
    /** 执行部门 */
    executeDepartment?: string;
    /** 项目地址 */
    projectAddress?: string;
    /** 状态 */
    status?: string;
    /** 成本估算（售后项目属性） */
    costEstimate?: number;
    /** 目前开销 */
    spend?: number;
    /** 已执行工作量（售后项目属性） */
    executedWorkload?: string;
    /** 项目进度（售后项目属性） */
    projectProgress?: number;
    /** 技术部绩效（售后项目属性） */
    techDepPerformance?: number;
    /** 项目概述 */
    description?: string;
    /** 审核状态 */
    activiStatus?: string;
    /** 是否自动审核通过 0 非自动 1 自动 */
    hasAutoActivi?: string;
    /** 设置全员可见 */
    allPersonAv?: boolean;
    /** 项目核算状态 */
    proPerformanceStatus?: string;
    /** 项目预算 SQ、SH、KF */
    projectBudget?: number;
    /** 售前成本 SH、KF */
    preCost?: number;
    /** 客户简称 */
    customerAbbreviation?: string;
  };

  type ProcessInstancesRankResp = {
    /** 流程实例id */
    processInstanceId?: string;
    /** 流程编号 */
    documentNumber?: string;
    /** 流程类型 */
    type?: string;
  };

  type ProHandedReq = {
    projectId?: string;
    userId?: string;
    username?: string;
    executeDepartment?: string;
  };

  type ProInfoDesReq = {
    /** 项目状况概述 */
    description?: string;
    /** 添加此条描述的时间 */
    addTime?: string;
  };

  type ProInfoDesResp = {
    /** 主键 */
    id?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目状况概述 */
    description?: string;
    /** 添加此条描述的时间 */
    addTime?: string;
  };

  type ProjectDeliveryResp = {
    /** 项目主键 */
    id?: string;
    /** 执行部门 */
    executeDepartment?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectClassify?: string;
    /** 项目状态 */
    status?: string;
    /** 客户主键 */
    clientId?: string;
    /** 客户简称 */
    clientAbbreviation?: string;
    /** 项目经理主键 */
    projectMangerId?: string;
    /** 项目经理姓名 */
    projectMangerName?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 销售人员姓名 */
    salePersonName?: string;
    /** 开销预估 */
    costEstimate?: number;
    /** 项目预算 */
    projectBudget?: number;
    /** 项目成本 */
    projectCost?: number;
    /** 售前成本 */
    preCost?: number;
    /** 当前开销 */
    spend?: number;
    /** 工作量预估 */
    estimatedWorkload?: string;
    /** 执行工作量 */
    executedWorkload?: string;
    /** 人工成本 */
    laborCost?: number;
    /** 利润比 */
    profitRatio?: number;
  };

  type ProjectNameIdResp = {
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目ID */
    id?: string;
  };

  type ProjectOpenAndCloseReq = {
    /** 要关闭的项目id */
    id?: string;
    /** 状态 */
    status?: string;
  };

  type ProjectPageProBaseInfoResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: ProBaseInfoResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
    /** 成本估算 */
    costEstimate?: number;
    /** 目前开销 */
    spend?: number;
  };

  type ProjectPayResp = {
    /** 核算状态 */
    performanceStatus?: string;
    /** 当前核算 */
    currentPerformance?: boolean;
    /** 核算编号 */
    perfNumber?: string;
    /** ID */
    id?: string;
    /** 项目ID */
    projectId?: string;
    /** 付款编号 */
    documentNumber?: string;
    /** 付款人ID */
    userId?: string;
    /** 付款人姓名 */
    username?: string;
    /** 付款类型 */
    paymentType?: string;
    /** 付款方式 */
    paymentMethod?: string;
    /** 付款金额 */
    paymentAmount?: string;
    /** 付款日期 */
    payTime?: string;
    /** 销账标识 */
    refundTag?: string;
    /** 备注 */
    remarks?: string;
  };

  type ProjectReq = {
    proBaseInfo?: ProBaseInfoReq;
    /** 项目情况描述请求对象集合 */
    proInfoDesList?: ProInfoDesReq[];
    /** 项目成员请求对象集合 */
    proMemberList?: ProMemberReq[];
    proRemark?: ProRemarkReq;
  };

  type ProjectResp = {
    proBaseInfo?: ProBaseInfoResp;
    /** 项目情况概述信息响应对象 */
    proInfoDesList?: ProInfoDesResp[];
    /** 项目成员信息响应对象 */
    proMemberList?: ProMemberResp[];
    proRemark?: ProRemarkResp;
    /** 项目支付明细 */
    weekInfoList?: WeekInfoResp[];
    /** 报销明细 */
    reimbursementList?: ReimbursementResp[];
    /** 付款明细 */
    paymentList?: Payment[];
    /** 培训报销明细 */
    trainReimburseList?: TrainReimbursementResp[];
  };

  type ProjectSqFeesDetailsResp = {
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 售前成本 */
    preSalesCost?: number;
    /** 项目周报-人力 */
    weekInfoFeesRespList?: WeekInfoFeesResp[];
    /** 项目报销 */
    reimbursementFeesRespList?: ReimbursementFeesResp[];
    /** 项目付款 */
    paymentFeesRespList?: PaymentFeesResp[];
  };

  type ProjectTypeResp = {
    /** 类型缩写 */
    abbreviation?: string;
    /** 类型 */
    type?: string;
    /** 编号规则 */
    serialNumberRule?: string;
  };

  type ProMangerReq = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 公司邮箱 */
    email?: string;
  };

  type ProMangerResp = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 公司邮箱 */
    email?: string;
  };

  type ProMemberReq = {
    /** 员工主键 */
    employeeId?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 加入时间 */
    joinTime?: string;
    /** 离开时间 */
    outTime?: string;
    /** 状态 */
    status?: string;
    /** 项目职责 */
    duty?: string;
    /** 项目成员贡献度集合 */
    bonusList?: BonusListReq[];
  };

  type ProMemberResp = {
    /** 主键（更新时传递） */
    id?: string;
    /** 项目基础信息对象主键 */
    projectId?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 加入时间 */
    joinTime?: string;
    /** 离开时间 */
    outTime?: string;
    /** 状态 */
    status?: string;
    /** 项目职责 */
    duty?: string;
    /** 项目成员贡献度集合 */
    bonusList?: BonusListReq[];
  };

  type Properties = {
    assignedType?: string;
    assignedUser?: string[];
    selfSelect?: Record<string, any>;
    leaderTop?: Record<string, any>;
    leader?: Record<string, any>;
    role?: Record<string, any>[];
    formUser?: string;
    nobody?: Record<string, any>;
    mode?: string;
    sign?: boolean;
    timeLimit?: { innerMap?: Record<string, any>; empty?: boolean };
    refuse?: Record<string, any>;
    formPerms?: FormOperates[];
    groupsType?: string;
    expression?: string;
    groups?: GroupsInfo[];
    shouldAdd?: boolean;
    type?: string;
    time?: number;
    unit?: string;
    dateTime?: string;
  };

  type ProRemarkReq = {
    /** 项目内容描述（售后项目属性） */
    proContentDes?: string;
    /** 项目备注 */
    proRemark?: string;
    /** 合同备注（售后项目属性） */
    contractRemark?: string;
  };

  type ProRemarkResp = {
    /** 主键（更新时传递） */
    id?: string;
    /** 项目基础信息主键 */
    projectId?: string;
    /** 项目内容描述（售后项目属性） */
    proContentDes?: string;
    /** 项目备注 */
    proRemark?: string;
    /** 合同备注（售后项目属性） */
    contractRemark?: string;
  };

  type PurConDetailResp = {
    /** 采购合同主键 */
    purContractId?: string;
    /** 采购合同编号 */
    purContractNumber?: string;
    /** 采购合同名称 */
    purContractName?: string;
    /** 采购合同类别 */
    serveCategory?: string;
    /** 采购合同金额 */
    contractAmount?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 最终用户 */
    endUser?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 已付款金额 */
    payedAmount?: number;
    /** 已收票金额 */
    receivedAmount?: number;
    /** 待付款金额 */
    awaitPayAmount?: number;
  };

  type PurContractDifferenceResp = {
    /** id */
    id?: string;
    /** 编号 */
    contractNumber?: string;
    /** 名称 */
    contractName?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 签订状态 */
    signStatus?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type PurContractInfoReq = {
    /** 主键 */
    id?: string;
    /** 服务类别 */
    serveCategory?: string;
    /** 框架合同 */
    isFrameworkAgreement?: number;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 主合同主键 */
    mainConId?: string;
    /** 主合同编号 */
    mainConNumber?: string;
    /** 主合同名称 */
    mainConName?: string;
    /** 主合同金额 */
    mainConAmount?: number;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 税额 */
    contractTax?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 备注 */
    remark?: string;
    /** 内容概述 */
    overview?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 最终用户 */
    endUser?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 客户简称 */
    customerAbbreviation?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 税率 */
    rate?: number;
    /** 行业 */
    industry?: string;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 计划交货日期 */
    estimatedDeliDate?: string;
    /** 差额 */
    difference?: number;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
  };

  type PurContractInfoResp = {
    /** 主键 */
    id?: string;
    /** 合同类别 */
    contractCategory?: string;
    /** 合同编号 */
    contractNumber?: string;
    isFrameworkAgreement?: number;
    /** 合同名称 */
    contractName?: string;
    /** 服务类别 */
    serveCategory?: string;
    /** 主合同主键 */
    mainConId?: string;
    /** 主合同编号 */
    mainConNumber?: string;
    /** 主合同名称 */
    mainConName?: string;
    /** 主合同金额 */
    mainAmount?: number;
    /** 合同状态 */
    contractStatus?: string;
    /** 合同金额 */
    contractAmount?: number;
    /** 税额 */
    contractTax?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 行业 */
    industry?: string;
    /** 甲方类型 */
    firstParty?: string;
    /** 甲方 */
    fpName?: string;
    /** 甲方主键 */
    fpId?: string;
    /** 乙方类型 */
    secondParty?: string;
    /** 乙方 */
    spName?: string;
    /** 乙方主键 */
    spId?: string;
    /** 销售 */
    salePerson?: string;
    /** 销售人员主键 */
    salePersonId?: string;
    /** 最终用户 */
    endUser?: string;
    /** 最终用户主键 */
    endUserId?: string;
    /** 合同所在地 */
    contractAddress?: string;
    /** 税率 */
    rate?: number;
    /** 签订日期 */
    signDate?: string;
    /** 签订状态 */
    signStatus?: string;
    /** 内容概述 */
    content?: string;
    /** 计划交货日期 */
    estimatedDeliDate?: string;
    /** 差额 */
    difference?: number;
    /** 备注 */
    remark?: string;
    /** 内容概述 */
    overview?: string;
    /** 实际终止日期 */
    actuallyStopTime?: string;
    /** 实际结束日期 */
    actuallyEndTime?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 创建人 */
    createdBy?: string;
  };

  type PurContractReq = {
    purContractInfo?: PurContractInfoReq;
    /** 合同内容请求参数集合 */
    contractContentList?: ContractContentReq[];
    payInfo?: PayInfoReq;
    /** 支付计划详细信息表 */
    payPlanDetailInfoList?: PayPlanDetailInfoReq[];
  };

  type PurContractResp = {
    purContractInfo?: PurContractInfoResp;
    /** 合同内容响应对象集合 */
    contractContentList?: ContractContentResp[];
    payInfo?: PayInfoResp;
    paySummary?: PaySummaryResp;
    /** 付款计划详细信息表 */
    payPlanDetailInfoList?: PayPlanDetailInfoResp[];
    /** 合同支付流水响应对象集合 */
    contractPayList?: PaymentResp[];
    /** 合同发票记录响应对象集合 */
    collectTicketContractList?: CollectTicketContractResp[];
  };

  type ReceiptsContractResp = {
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
  };

  type ReceiptsInfoResp = {
    /** 主键 */
    id?: string;
    /** 收款期次 */
    period?: number;
    /** 关联收款计划主键 */
    collPlanId?: string;
    /** 支付流水主键 */
    paymentId?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 关联合同信息 */
    contractList?: ReceiptsContractResp[];
    /** 收款编号 */
    collectNumber?: string;
    /** 收款时间 */
    receiveTime?: string;
    /** 收款方式 */
    collectionWay?: string;
    /** 收款类别 */
    collectionType?: string;
    /** 收款状态 */
    collectionStatus?: string;
    /** 收款金额 */
    receiveAmount?: number;
    /** 业务伙伴主键 */
    partnerId?: string;
    /** 业务伙伴名称 */
    partnerName?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 摘要 */
    overview?: string;
    /** 用途 */
    useWay?: string;
    /** 详细信息 */
    remark?: string;
    /** 录入时间 */
    inputTime?: string;
    /** 认领日期 */
    claimTime?: string;
    /** 认领人员主键 */
    claimUserId?: string;
    /** 认领人 */
    claimUser?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 保证金主键 */
    marginId?: string;
    /** 保证金编号 */
    documentNumber?: string;
    /** 退款流水编号 */
    refundNumber?: string;
    collectionPlanRespList?: CollectionPlanResp[];
    paymentApplicationPageRespList?: PaymentApplicationPageResp[];
  };

  type ReceiptsPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortReceiveAmount?: string;
    sortReceiveTime?: string;
  };

  type ReceiptsPlanReq = {
    /** 关联收款计划主键 */
    collPlanId?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
  };

  type ReceiptsReq = {
    /** 主键 */
    id?: string;
    /** 关联收款计划 */
    collPlan?: ReceiptsPlanReq[];
    /** 支付流水主键 */
    paymentId?: string;
    /** 收款编号 */
    collectNumber?: string;
    /** 收款时间 */
    receiveTime?: string;
    /** 收款方式 */
    collectionWay?: string;
    /** 收款类别 */
    collectionType?: string;
    /** 收款状态 */
    collectionStatus?: string;
    /** 收款金额 */
    receiveAmount?: number;
    /** 业务伙伴主键 */
    partnerId?: string;
    /** 业务伙伴名称 */
    partnerName?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 摘要 */
    overview?: string;
    /** 用途 */
    useWay?: string;
    /** 详细信息 */
    remark?: string;
    /** 录入时间 */
    inputTime?: string;
    /** 认领日期 */
    claimTime?: string;
    /** 认领人员主键 */
    claimUserId?: string;
    /** 认领人 */
    claimUser?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 保证金主键 */
    marginId?: string;
    /** 保证金编号 */
    documentNumber?: string;
  };

  type ReceiptsResp = {
    /** 主键 */
    id?: string;
    /** 收款期次 */
    period?: number;
    /** 关联收款计划主键 */
    collPlanId?: string;
    /** 支付流水主键 */
    paymentId?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同编号 */
    contractNumber?: string;
    /** 合同名称 */
    contractName?: string;
    /** 关联合同信息 */
    contractList?: ReceiptsContractResp[];
    /** 收款编号 */
    collectNumber?: string;
    /** 收款时间 */
    receiveTime?: string;
    /** 收款方式 */
    collectionWay?: string;
    /** 收款类别 */
    collectionType?: string;
    /** 收款状态 */
    collectionStatus?: string;
    /** 收款金额 */
    receiveAmount?: number;
    /** 业务伙伴主键 */
    partnerId?: string;
    /** 业务伙伴名称 */
    partnerName?: string;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 银行账户 */
    account?: string;
    /** 摘要 */
    overview?: string;
    /** 用途 */
    useWay?: string;
    /** 详细信息 */
    remark?: string;
    /** 录入时间 */
    inputTime?: string;
    /** 认领日期 */
    claimTime?: string;
    /** 认领人员主键 */
    claimUserId?: string;
    /** 认领人 */
    claimUser?: string;
    /** 认领状态 */
    claimStatus?: string;
    /** 保证金主键 */
    marginId?: string;
    /** 保证金编号 */
    documentNumber?: string;
    /** 退款流水编号 */
    refundNumber?: string;
  };

  type RecruitInfoResp = {
    /** id */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 申请人 */
    applicantId?: string;
    /** 申请人姓名 */
    applicantName?: string;
    /** 申请日期 */
    applicationTime?: string;
    /** 期望到岗日期 */
    expectationTime?: string;
    /** 需岗位等级 */
    demandGrade?: string;
    /** 工作属地 */
    territory?: string;
    /** 申请原因 */
    applicationCause?: string;
    /** 性别 */
    gender?: string;
    /** 性别描述 */
    genderDesc?: string;
    /** 年龄 */
    age?: string;
    /** 年龄描述 */
    ageDesc?: string;
    /** 薪酬 */
    wages?: string;
    /** 薪酬描述 */
    wagesDesc?: string;
    /** 行业背景 */
    industryBack?: string;
    /** 行业背景描述 */
    industryBackDesc?: string;
    /** 工作年限 */
    workYears?: string;
    /** 工作年限描述 */
    workYearsDesc?: string;
    /** 跳槽频率 */
    frequency?: string;
    /** 跳槽频率描述 */
    frequencyDesc?: string;
    /** 学历 */
    degree?: string;
    /** 学历描述 */
    degreeDesc?: string;
    /** 专业 */
    specialized?: string;
    /** 专业描述 */
    specializedDesc?: string;
    /** 优先条件 */
    priorityCondition?: string;
    /** 其他补充要求 */
    chenCheng?: string;
    /** 岗位职责 */
    duty?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 岗位名称 */
    positionNameId?: string;
    /** 需求人数 */
    positionNum?: string;
    /** 隶属部门 */
    affiliationDepartmentId?: string;
    /** 招聘需求 */
    recruitDemand?: string;
  };

  type RecruitInsertReq = {
    /** 申请人id */
    applicantId?: string;
    /** 申请人姓名 */
    applicantName?: string;
    /** 申请日期 */
    applicationTime?: string;
    /** 期望到岗日期 */
    expectationTime?: string;
    /** 需岗位等级 */
    demandGrade?: string;
    /** 工作属地 */
    territory?: string;
    /** 申请原因 */
    applicationCause?: string;
    /** 性别 */
    gender?: string;
    genderDesc?: string;
    /** 年龄 */
    age?: string;
    ageDesc?: string;
    /** 薪酬 */
    wages?: string;
    wagesDesc?: string;
    /** 行业背景 */
    industryBack?: string;
    industryBackDesc?: string;
    /** 工作年限 */
    workYears?: string;
    workYearsDesc?: string;
    /** 跳槽频率 */
    frequency?: string;
    frequencyDesc?: string;
    /** 学历 */
    degree?: string;
    degreeDesc?: string;
    /** 专业 */
    specialized?: string;
    specializedDesc?: string;
    /** 优先条件 */
    priorityCondition?: string;
    /** 其他补充要求 */
    chenCheng?: string;
    /** 岗位职责 */
    duty?: string;
    /** 岗位名称 */
    positionNameId?: string;
    /** 需求人数 */
    positionNum?: string;
    /** 隶属部门 */
    affiliationDepartmentId?: string;
    /** 招聘需求 */
    recruitDemand?: string;
  };

  type RecruitUpdateReq = {
    /** 申请人id */
    id?: string;
    /** 申请人id */
    applicantId?: string;
    /** 申请人姓名 */
    applicantName?: string;
    /** 申请日期 */
    applicationTime?: string;
    /** 期望到岗日期 */
    expectationTime?: string;
    /** 需岗位等级 */
    demandGrade?: string;
    /** 工作属地 */
    territory?: string;
    /** 申请原因 */
    applicationCause?: string;
    /** 性别 */
    gender?: string;
    genderDesc?: string;
    /** 年龄 */
    age?: string;
    ageDesc?: string;
    /** 薪酬 */
    wages?: string;
    wagesDesc?: string;
    /** 行业背景 */
    industryBack?: string;
    industryBackDesc?: string;
    /** 工作年限 */
    workYears?: string;
    workYearsDesc?: string;
    /** 跳槽频率 */
    frequency?: string;
    frequencyDesc?: string;
    /** 学历 */
    degree?: string;
    degreeDesc?: string;
    /** 专业 */
    specialized?: string;
    specializedDesc?: string;
    /** 优先条件 */
    priorityCondition?: string;
    /** 其他补充要求 */
    chenCheng?: string;
    /** 岗位职责 */
    duty?: string;
    /** 岗位名称 */
    positionNameId?: string;
    /** 需求人数 */
    positionNum?: string;
    /** 隶属部门 */
    affiliationDepartmentId?: string;
    /** 招聘需求 */
    recruitDemand?: string;
  };

  type RefreshFromDataReq = {
    /** 流程类型 */
    type?: string;
    /** 被审核的单据ID */
    businessKey?: string;
  };

  type RefundPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortRefAmount?: string;
  };

  type RefundReq = {
    /** 主键 */
    id?: string;
    /** 支付流水号 */
    payNumber?: string;
    /** 支付流水号主键 */
    paymentId?: string;
    /** 支付方式 */
    payWay?: string;
    /** 付款日期 */
    payTime?: string;
    /** 付款金额 */
    payAmount?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 账户 */
    account?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 部门 */
    department?: string;
    /** 申请人 */
    applicationUser?: string;
    /** 申请人员主键 */
    applicationUserId?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 描述 */
    description?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 预计退款时间 */
    estimateRefundTime?: string;
    /** 退款时间 */
    refundTime?: string;
    /** 销账标志 */
    refundTag?: string;
    /** 收款流水号 */
    collectNumber?: string;
    /** 收款流水号主键 */
    receiptId?: string;
    /** 保证金主键 */
    marginId?: string;
  };

  type RefundResp = {
    /** 主键 */
    id?: string;
    /** 退款流水 */
    refundNumber?: string;
    /** 保证金编号 */
    marginNumber?: string;
    /** 支付流水号 */
    payNumber?: string;
    /** 支付流水号主键 */
    paymentId?: string;
    /** 支付方式 */
    payWay?: string;
    /** 付款日期 */
    payTime?: string;
    /** 付款金额 */
    payAmount?: number;
    /** 单位名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 账户 */
    account?: string;
    /** 备注 */
    remark?: string;
    /** 用途 */
    useWay?: string;
    /** 部门 */
    department?: string;
    /** 申请人 */
    applicationUser?: string;
    /** 申请人员主键 */
    applicationUserId?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 描述 */
    description?: string;
    /** 资金类别 */
    fundingCategory?: string;
    /** 预计退款时间 */
    estimateRefundTime?: string;
    /** 退款时间 */
    refundTime?: string;
    /** 销账标志 */
    refundTag?: string;
    /** 收款流水号 */
    collectNumber?: string;
    /** 收款流水号主键 */
    receiptId?: string;
    collectId?: string;
    /** 保证金主键 */
    marginId?: string;
    /** 退款金额 */
    refAmount?: string;
  };

  type RefuseWorkFlowReq = {
    /** 流程类型 */
    type?: string;
    /** 被审核的单据ID */
    processInstanceId?: string;
    /** 意见 */
    comments?: string;
  };

  type ReimbursementFeesResp = {
    /** 主键 */
    id?: string;
    /** 报销主键 */
    finaId?: string;
    /** 报销编号 */
    finaNumber?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 报销时间 */
    time?: string;
    /** 报销类型 */
    actualType?: string;
    /** 报销金额 */
    actualMoney?: string;
    /** 成本 */
    cost?: number;
    /** 报销理由 */
    reason?: string;
    /** 成本状态 0：未关联 1：当前合同关联 2：其他合同关联 */
    costStatus?: string;
  };

  type ReimbursementResp = {
    /** 报销主键 */
    finaId?: string;
    /** 报销编号 */
    finaNumber?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 报销时间 */
    time?: string;
    /** 报销类型 */
    actualType?: string;
    /** 报销金额 */
    actualMoney?: string;
    /** 报销理由 */
    reason?: string;
  };

  type RelevancySqFeesReq = {
    /** 主合同id */
    contractId?: string;
    /** 售前成本 */
    preSalesCost?: number;
    /** 售前成本id */
    feesList?: FeesDetailReq[];
  };

  type ResultActiviInfoMobileResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ActiviInfoMobileResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultActiviInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ActiviInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultAnnouncementPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AnnouncementPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultApprovalResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ApprovalResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultAptitudeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AptitudeResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultAttendanceMonthResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AttendanceMonthResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultAuthResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AuthResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultBillingInformationDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: BillingInformationDTO;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultBoolean = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: boolean;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultCertificateResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: CertificateResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultCollectionString = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: string[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultCollectTicketInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: CollectTicketInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultContractReceiptsAndPaymentsResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ContractReceiptsAndPaymentsResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultCoopAgreeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: CoopAgreeResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultDictionaryInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: DictionaryInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultEmployeeGradeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: EmployeeGradeResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultEmployeeInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: EmployeeInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultFinaCountResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: FinaCountResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultFinaInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: FinaInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultFirmResourceInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: FirmResourceInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultFlowPagePaymentResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: FlowPagePaymentResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultFlowPageRefundResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: FlowPageRefundResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultHistoricProcessInstancePageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: HistoricProcessInstancePageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultHolidayInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: HolidayInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultInnerContractResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: InnerContractResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultInteger = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: number;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultInvoicingResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: InvoicingResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultLeaveAppInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: LeaveAppInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultLeavePolicyInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: LeavePolicyInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultLeaveSellInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: LeaveSellInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListBanksDto = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: BanksDto[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListBrieflyProInfo = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: BrieflyProInfo[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListCalendarResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: CalendarResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListCollectionPlanResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: CollectionPlanResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListCollectTicketContractListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: CollectTicketContractListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListContractPaymentResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ContractPaymentResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListCurrentUserWorkResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: CurrentUserWorkResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListDepartmentAndUserTreeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: DepartmentAndUserTreeResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListDepartmentListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: DepartmentListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListDepartmentTreeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: DepartmentTreeResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListDepartmentWeekTreeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: DepartmentWeekTreeResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListDict = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: Dict[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListDictionaryListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: DictionaryListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListEmployeeGradeListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: EmployeeGradeListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListEmploymentTypeListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: EmploymentTypeListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListExportDepartmentResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ExportDepartmentResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListExportUserResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ExportUserResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListHistoricalContactsResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: HistoricalContactsResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListHolidayDetailsListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: HolidayDetailsListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListLeaveBalanceListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: LeaveBalanceListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListLeaveTypeListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: LeaveTypeListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListMainContractListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: MainContractListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListOperationRecord = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: OperationRecord[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListPartnerInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: PartnerInfoResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListPaymentApplicationPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: PaymentApplicationPageResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListPaymentResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: PaymentResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListPositionListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: PositionListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListProBaseInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ProBaseInfoResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListProcessInstancesRankResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ProcessInstancesRankResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListProjectNameIdResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ProjectNameIdResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListProjectTypeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ProjectTypeResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListSysCertificateListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: SysCertificateListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListTaskLogListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: TaskLogListResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListUserAddressBookResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: UserAddressBookResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListUserPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: UserPageResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultListUserScheduleResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: UserScheduleResp[];
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultMainContractDifferenceResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: MainContractDifferenceResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultMainContractExtraResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: MainContractExtraResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultMainContractResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: MainContractResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultMapStringLong = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: Record<string, any>;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultObject = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: Record<string, any>;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageAnnouncementPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageAnnouncementPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageAptitudeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageAptitudeResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageAttRecordListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageAttRecordListResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageCertificateResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageCertificateResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageCollectTicketPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageCollectTicketPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageContractPayResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageContractPayResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageContractProfitResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageContractProfitResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageCoopAgreeResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageCoopAgreeResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageFinaPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageFinaPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageFirmResourceInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageFirmResourceInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageHolidayDetailsPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageHolidayDetailsPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageHolidayPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageHolidayPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageInitialPerformanceAppraisalTableResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageInitialPerformanceAppraisalTableResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageInnerConInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageInnerConInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageInstitutionListResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageInstitutionListResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageInvoicingResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageInvoicingResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageLeaveAppPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageLeaveAppPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageLeavePolicyPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageLeavePolicyPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageLeaveSellInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageLeaveSellInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageLeaveTypePageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageLeaveTypePageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageMainContractInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageMainContractInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePartnerInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePartnerInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePaymentApplicationPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePaymentApplicationPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePaymentAppResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePaymentAppResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePendingInvoiceResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePendingInvoiceResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePendingReceiptContractResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePendingReceiptContractResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePendingReceiptProjectResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePendingReceiptProjectResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageProjectDeliveryResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageProjectDeliveryResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPagePurContractInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PagePurContractInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageReceiptsResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageReceiptsResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageRecruitInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageRecruitInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageRoleInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageRoleInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageRulesResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageRulesResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageTalentInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageTalentInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageTaskPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageTaskPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageTenderResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageTenderResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageTrainReimbursementResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageTrainReimbursementResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageUserPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageUserPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageUserSalaryResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageUserSalaryResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageUserScheduleResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageUserScheduleResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageWeekPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageWeekPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPageWorkOrderPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageWorkOrderPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPartnerResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PartnerResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPaymentApplicationInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PaymentApplicationInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPaymentApplicationLogoffResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PaymentApplicationLogoffResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPaymentAppResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PaymentAppResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPaymentPagePaymentResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PaymentPagePaymentResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPaymentResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PaymentResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPerformanceConfResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PerformanceConfResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPerformancePagePerMainResultResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PerformancePagePerMainResultResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPrintInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PrintInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultProjectPageProBaseInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ProjectPageProBaseInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultProjectResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ProjectResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultProjectSqFeesDetailsResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ProjectSqFeesDetailsResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultPurContractResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PurContractResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultReceiptsInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ReceiptsInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultReceiptsResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ReceiptsResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultRecruitInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RecruitInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultRefundResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RefundResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultRoleInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RoleInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultRulesAndViewInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RulesAndViewInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultString = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: string;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultSysDataDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: SysDataDTO;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultTalentInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TalentInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultTaskCountResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TaskCountResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultTaskPageResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TaskPageResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultTenderResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TenderResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultTrainReimbursementResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TrainReimbursementResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultTreatSubmitResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TreatSubmitResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultUserDetailResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserDetailResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultUserInfo = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserInfo;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultWeekAnalysisResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: WeekAnalysisResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultWeekInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: WeekInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type ResultWorkOrderInfoResp = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: WorkOrderInfoResp;
    /** 接口请求时间 */
    timestamp?: number;
  };

  type RoleAssignment2UserReq = {
    /** 角色id */
    roleId?: string;
    /** 用户ids */
    userIds?: string[];
  };

  type RoleInfoResp = {
    /** ID */
    id?: string;
    /** 角色名称 */
    name?: string;
    /** 角色描述 */
    description?: string;
    /** 角色状态 */
    status?: string;
    /** 查看权限 */
    pagePermissions?: string[];
    /** 数据权限 */
    dataPermissions?: string[];
    /** 已分配用户 */
    userIds?: string[];
  };

  type RoleInsertReq = {
    /** 角色名称 */
    name?: string;
    /** 角色描述 */
    description?: string;
    /** 查看权限 */
    pagePermissions?: string[];
    /** 数据权限 */
    dataPermissions?: string[];
  };

  type RoleUpdateReq = {
    id?: string;
    /** 角色名称 */
    name?: string;
    /** 角色描述 */
    description?: string;
    /** 查看权限 */
    pagePermissions?: string[];
    /** 数据权限 */
    dataPermissions?: string[];
  };

  type RoleUpdateStatusReq = {
    /** ID */
    id?: string;
    /** 状态 0激活 1禁用 */
    status?: string;
  };

  type RulesAndViewInfoResp = {
    rulesResp?: RulesResp;
    /** 浏览该制度相关人员以及意见信息 */
    viewRulesRespList?: UserViewRulesResp[];
    /** 制度通知人员集合 */
    noticeUserRespList?: NoticeUserResp[];
  };

  type RulesReq = {
    /** 主键 */
    id?: string;
    /** 制度名称 */
    rulesName?: string;
    /** 制度状态 */
    status?: string;
    /** 制度开始执行时间 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
    /** 文件地址 */
    filePath?: string;
  };

  type RulesResp = {
    /** 主键 */
    id?: string;
    /** 制度名称 */
    rulesName?: string;
    /** 文件名称 */
    fileName?: string;
    /** 文件地址 */
    filePath?: string;
    /** 制度状态 */
    status?: string;
    /** 制度开始执行时间 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
    /** 上传时间 */
    uploadTime?: string;
    /** 已查看人数 */
    hasView?: number;
    /** 已确认人数 */
    hasConfirm?: number;
    /** 有意见人数 */
    hasOpinion?: number;
    /** 当前用户状态 */
    currentUserState?: boolean;
    /** 分配用户数量 */
    assignUserCount?: number;
    /** 是否置顶 */
    hasTop?: number;
    /** 置顶时间 */
    topTime?: string;
  };

  type SalaryInsertReq = {
    /** 员工主键 */
    employeeId?: string;
    /** 调整日期 */
    adjustmentDate?: string;
    /** 调整原因 */
    changeReason?: string;
    /** 基本工资 */
    baseSalary?: string;
    /** 税前工资 */
    preTaxSalary?: string;
    /** 其他 */
    otherSalary?: string;
    /** 福利等级 */
    benefitLevelId?: string;
    /** 备注 */
    remark?: string;
    /** AES密钥 */
    aesKey?: string;
  };

  type SalaryUpdateReq = {
    /** 员工主键 */
    employeeId?: string;
    /** 调整原因 */
    changeReason?: string;
    /** 基本工资 */
    baseSalary?: string;
    /** 税前工资 */
    preTaxSalary?: string;
    /** 福利等级 */
    benefitLevelId?: string;
    /** 其他 */
    otherSalary?: string;
    /** 备注 */
    remark?: string;
    /** AES密钥 */
    aesKey?: string;
  };

  type SaleCollectionPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortEstimateReAmount?: string;
    sortEstimateReTime?: string;
    sortTicketTime?: string;
  };

  type SalePaymentPageReq = {
    /** 页码 */
    pageNum?: number;
    /** 每页长度 */
    pageSize?: number;
    /** 利润比排序 */
    sortProfitRatio?: string;
    search?: Record<string, any>;
    filter?: Record<string, any>;
    scope?: ScopeDate[];
    extra?: Record<string, any>;
    sortEstimatePayTime?: string;
    sortEstimatePayAmount?: string;
    sortCountInvoicedTime?: string;
  };

  type SalePersonReq = {
    /** 主键 */
    id?: string;
    /** 业务伙伴主键 */
    partnerId?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 部门 */
    department?: string;
    /** 账号 */
    account?: string;
  };

  type SalePersonResp = {
    /** 主键 */
    id?: string;
    /** 业务伙伴主键 */
    partnerId?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 部门 */
    department?: string;
    /** 账号 */
    account?: string;
  };

  type SalePlanReq = {
    /** 主键 */
    id?: string;
    /** 客户主键 */
    partnerId?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 项目类别 */
    projectClassify?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目描述 */
    projectDescription?: string;
    /** 项目概率 */
    proProgress?: string;
    /** 销售金额 */
    saleAmount?: number;
    /** 采购成本 */
    purchaseCost?: number;
    /** 销售成员 */
    salePerson?: string;
    /** 销售成员主键 */
    salePersonId?: string;
    /** 招标计划 */
    remark?: string;
  };

  type SalePlanResp = {
    /** 主键 */
    id?: string;
    /** 客户主键 */
    partnerId?: string;
    /** 项目主键 */
    projectId?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 项目类别 */
    projectClassify?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目描述 */
    projectDescription?: string;
    /** 项目概率 */
    proProgress?: string;
    /** 销售金额 */
    saleAmount?: number;
    /** 采购成本 */
    purchaseCost?: number;
    /** 销售成员 */
    salePerson?: string;
    /** 销售成员主键 */
    salePersonId?: string;
    /** 招标计划 */
    remark?: string;
  };

  type SalesRatioReq = {
    /** 用户ID */
    userId: string;
    /** 用户名 */
    username: string;
    /** 提成比例 */
    ratio: number;
  };

  type SalesRatioResp = {
    /** 用户ID */
    userId?: string;
    /** 用户名 */
    username?: string;
    /** 提成比例 */
    ratio?: number;
  };

  type schedulerListParams = {
    category: string;
  };

  type ScopeDate = {
    name?: string;
    key?: string;
    val?: string;
  };

  type selectByPartnerIdParams = {
    idReq: IdReq;
  };

  type selectCollPlan1Params = {
    institutionName: string;
  };

  type selectCollPlanParams = {
    idReq: IdReq;
  };

  type selectInvoicedByIdParams = {
    idReq: IdReq;
  };

  type selectPaymentAppByIdParams = {
    idReq: IdReq;
  };

  type selectPaymentByIdParams = {
    idReq: IdReq;
  };

  type selectProjectByIdParams = {
    /** 主键请求参数对象 */
    idReq: IdReq;
  };

  type selectReceiptByIdParams = {
    idReq: IdReq;
  };

  type selectReceiptParams = {
    idReq: IdReq;
  };

  type selectRefundByIdParams = {
    idReq: IdReq;
  };

  type selectRulesByIdParams = {
    idReq: IdReq;
  };

  type selectTrainReByIdParams = {
    idReq: IdReq;
  };

  type StartWorkFlowReq = {
    /** 流程类型 */
    type?: string;
    /** 被审核的单据ID */
    businessKey?: string;
  };

  type SubsidyForm = {
    subsidyType?: string;
    subsidyMoney?: string;
  };

  type SysCertificateInsertReq = {
    /** 类型 */
    type?: string;
  };

  type SysCertificateListResp = {
    /** ID */
    id?: string;
    /** 类型 */
    type?: string;
  };

  type SysCertificateUpdateReq = {
    /** ID */
    id?: string;
    /** 类型 */
    type?: string;
  };

  type SysDataDTO = {
    /** 月工作日 */
    monthlyWorkdays?: number;
    /** 工资系数 */
    salaryCoefficient?: number;
    /** 售前项目人力工资基数(人天) */
    preDaySalary?: number;
  };

  type TalentInfoResp = {
    /** ID */
    id?: string;
    /** 姓名 */
    username?: string;
    /** 性别 */
    gender?: string;
    /** 出生年月 */
    dateOfBirth?: string;
    /** 邮箱 */
    email?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 学历 */
    degree?: string;
    /** 主要技能 */
    keySkill?: string;
    /** 证书 */
    certificate?: string;
    /** 专业 */
    specialized?: string;
    /** 毕业院校 */
    graduateSchool?: string;
    /** 应聘职位 */
    positionId?: string;
    /** 简历投递日期 */
    deliveryDate?: string;
    /** 简历筛选结果 */
    filterTheResults?: string;
    /** 初面时间 */
    introductoryTime?: string;
    /** 初面结果 */
    introductoryOutcome?: string;
    /** 笔试时间 */
    writtenTime?: string;
    /** 笔试结果 */
    writtenOutcome?: string;
    /** 简历附件 */
    resumeAnnex?: string;
  };

  type TalentInsertReq = {
    /** 姓名 */
    username?: string;
    /** 性别 */
    gender?: string;
    /** 出生年月 */
    dateOfBirth?: string;
    /** 邮箱 */
    email?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 学历 */
    degree?: string;
    /** 主要技能 */
    keySkill?: string;
    /** 证书 */
    certificate?: string;
    /** 专业 */
    specialized?: string;
    /** 毕业院校 */
    graduateSchool?: string;
    /** 应聘职位 */
    positionId?: string;
    /** 简历投递日期 */
    deliveryDate?: string;
    /** 简历筛选结果 */
    filterTheResults?: string;
    /** 初面时间 */
    introductoryTime?: string;
    /** 初面结果 */
    introductoryOutcome?: string;
    /** 笔试时间 */
    writtenTime?: string;
    /** 笔试结果 */
    writtenOutcome?: string;
    /** 简历附件 */
    resumeAnnex?: string;
  };

  type TalentUpdateReq = {
    /** id */
    id?: string;
    /** 姓名 */
    username?: string;
    /** 性别 */
    gender?: string;
    /** 出生年月 */
    dateOfBirth?: string;
    /** 邮箱 */
    email?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 学历 */
    degree?: string;
    /** 主要技能 */
    keySkill?: string;
    /** 证书 */
    certificate?: string;
    /** 专业 */
    specialized?: string;
    /** 毕业院校 */
    graduateSchool?: string;
    /** 应聘职位 */
    positionId?: string;
    /** 简历投递日期 */
    deliveryDate?: string;
    /** 简历筛选结果 */
    filterTheResults?: string;
    /** 初面时间 */
    introductoryTime?: string;
    /** 初面结果 */
    introductoryOutcome?: string;
    /** 笔试时间 */
    writtenTime?: string;
    /** 笔试结果 */
    writtenOutcome?: string;
    /** 简历附件 */
    resumeAnnex?: string;
  };

  type TaskAssignReq = {
    /** 任务id */
    id: string;
    /** 执行用户 */
    executeUserId: string;
  };

  type TaskCountResp = {
    /** 我发起 */
    i_initiated?: number;
    /** 未开始 */
    not_started?: number;
    /** 执行中 */
    in_progress?: number;
    /** 已终止 */
    terminated?: number;
    /** 已提交 */
    submitted?: number;
    /** 按时完成 */
    completed_on_time?: number;
    /** 逾期完成 */
    completed_overdue?: number;
  };

  type TaskDescribeReq = {
    /** ID */
    id?: string;
    /** 任务ID */
    taskId?: string;
    /** 任务日期 */
    taskDate?: string;
    /** 任务情况描述 */
    content?: string;
  };

  type TaskDescribeResp = {
    /** 任务日期 */
    taskDate?: string;
    /** 任务情况描述 */
    content?: string;
  };

  type TaskLogListResp = {
    /** 日志内容 */
    content?: string;
    /** 操作时间 */
    createdTime?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
  };

  type TaskLogReq = {
    /** ID */
    id?: string;
    /** 排序 */
    sort?: string;
  };

  type TaskPageResp = {
    id?: string;
    /** 父级任务ID */
    parentId?: string;
    /** 编号 */
    documentNumber?: string;
    /** 任务名称 */
    taskName?: string;
    /** 发起部门 */
    launchDepartmentId?: string;
    /** 发起用户 */
    launchUserId?: string;
    /** 发起时间 */
    launchTime?: string;
    /** 计划结束时间 */
    planEndTime?: string;
    /** 执行部门 */
    executeDepartmentId?: string;
    /** 执行用户 */
    executeUserId?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 关闭时间 */
    closeTime?: string;
    /** 任务状态 */
    taskStatus?: string;
    /** 任务描述 */
    taskDesc?: string;
    /** 抄送范围 */
    carbonCopyScope?: string;
    /** 抄送人 */
    carbonCopyIds?: string[];
    /** 是否逾期 true 逾期 */
    hasOverdue?: boolean;
    /** 完成进度 0-100 */
    progress?: number;
    /** 发起用户 */
    launchUserName?: string;
    /** 执行用户 */
    executeUserName?: string;
    /** 抄送人 */
    taskDescribeList?: TaskDescribeResp[];
    /** 子任务 */
    children?: TaskPageResp[];
  };

  type TaskPublishReq = {
    /** 任务名称 */
    taskName: string;
    /** 发起部门 */
    launchDepartmentId: string;
    /** 发起用户 */
    launchUserId: string;
    /** 抄送范围 */
    carbonCopyScope?: string;
    /** 抄送人 */
    carbonCopyIds?: string[];
    /** 计划结束时间 */
    planEndTime: string;
    /** 任务描述 */
    taskDesc: string;
  };

  type TaskSplitReq = {
    /** 父级任务ID */
    parentId: string;
    /** 任务名称 */
    taskName: string;
    /** 任务执行人ID */
    executeUserId: string;
    /** 计划结束时间 */
    planEndTime: string;
    /** 任务描述 */
    taskDesc: string;
  };

  type TaskUpdateChildrenReq = {
    /** 任务ID */
    id: string;
    /** 任务名称 */
    taskName: string;
    /** 计划结束时间 */
    planEndTime: string;
    /** 执行用户 */
    executeUserId: string;
    /** 任务描述 */
    taskDesc: string;
  };

  type TaskUpdateReq = {
    /** 任务ID */
    id: string;
    /** 任务名称 */
    taskName: string;
    /** 计划结束时间 */
    planEndTime: string;
    /** 任务描述 */
    taskDesc: string;
    /** 抄送范围 */
    carbonCopyScope?: string;
    /** 抄送人 */
    carbonCopyIds?: string[];
    /** 任务情况描述 */
    taskDescribeList?: TaskDescribeReq[];
  };

  type TenderReq = {
    /** 主键 */
    id?: string;
    /** 平台名称 */
    platformName?: string;
    /** 公司名称 */
    companyName?: string;
    /** 备注 */
    remark?: string;
    /** 网站 */
    websiteName?: string;
    /** 账号 */
    account?: string;
    /** 密码 */
    password?: string;
    /** 邮箱 */
    email?: string;
    /** ca密码 */
    caPassword?: string;
    /** ca到期时间 */
    expireTime?: string;
    /** ca/平台续费时间 */
    renewalTime?: string;
    /** 责任人 */
    liabilityPerson?: string;
    /** 负责人主键 */
    liabilityPersonId?: string;
    /** 联系人 */
    contactPerson?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 逻辑删除标志 */
    deleteIf?: number;
    /** 补充字段 */
    supplement?: Record<string, any>[];
  };

  type TenderResp = {
    /** 主键 */
    id?: string;
    /** 编号 */
    tenderNumber?: string;
    /** 平台名称 */
    platformName?: string;
    /** 公司名称 */
    companyName?: string;
    /** 备注 */
    remark?: string;
    /** 网站 */
    websiteName?: string;
    /** 账号 */
    account?: string;
    /** 密码 */
    password?: string;
    /** 邮箱 */
    email?: string;
    /** ca密码 */
    caPassword?: string;
    /** ca到期时间 */
    expireTime?: string;
    /** ca/平台续费时间 */
    renewalTime?: string;
    /** 责任人 */
    liabilityPerson?: string;
    /** 负责人主键 */
    liabilityPersonId?: string;
    /** 联系人 */
    contactPerson?: string;
    /** 联系方式 */
    contactNumber?: string;
    /** 补充字段 */
    supplement?: Record<string, any>[];
  };

  type TicketInfoReq = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 机构名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 联系电话 */
    contactNumber?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 银行账户 */
    account?: string;
    /** 注册地址 */
    registeredAddress?: string;
  };

  type TicketInfoResp = {
    /** 主键 */
    id?: string;
    /** 合同主键 */
    contractId?: string;
    /** 合同类型 */
    contractType?: string;
    /** 机构名称 */
    institutionName?: string;
    /** 开户银行 */
    bank?: string;
    /** 联系电话 */
    contactNumber?: string;
    /** 纳税人识别号 */
    taxpayerNum?: string;
    /** 银行账户 */
    account?: string;
    /** 注册地址 */
    registeredAddress?: string;
  };

  type TrainReimbursementReq = {
    /** 主键 */
    id?: string;
    /** 编号 */
    trainNumber?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 培训费用 */
    trainSpend?: number;
    /** 培训内容 */
    trainContent?: string;
    /** 发票提交状态 */
    operateState?: string;
    /** 返还次数 */
    count?: number;
    /** 返还开始时间 */
    startTime?: string;
    /** 返还结束时间 */
    endTime?: string;
    /** 每月返还金额 */
    amount?: number;
    /** 剩余返回次数 */
    remainderCount?: number;
    /** 剩余返还金额 */
    remainderAmount?: number;
    /** 次年返还金额 */
    nextYearAmount?: number;
    /** 返还状态 */
    state?: string;
    /** 项目id */
    projectId?: string;
  };

  type TrainReimbursementResp = {
    /** 主键 */
    id?: string;
    /** 编号 */
    trainNumber?: string;
    /** 员工主键 */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 培训费用 */
    trainSpend?: number;
    /** 培训内容 */
    trainContent?: string;
    /** 发票提交状态 */
    operateState?: string;
    /** 返还次数 */
    count?: number;
    /** 返还开始时间 */
    startTime?: string;
    /** 返还结束时间 */
    endTime?: string;
    /** 每月返还金额 */
    amount?: number;
    /** 剩余返回次数 */
    remainderCount?: number;
    /** 剩余返还金额 */
    remainderAmount?: number;
    /** 次年返还金额 */
    nextYearAmount?: number;
    /** 返还状态 */
    state?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 项目id */
    projectId?: string;
  };

  type TrainReimbursementStatusUpdateReq = {
    /** 主键 */
    id?: string;
    /** 返还状态 */
    state?: string;
  };

  type TreatSubmitResp = {
    count?: number;
    treatSubmitRespList?: Record<string, any>[];
  };

  type UserAddressBookResp = {
    /** ID */
    id?: string;
    /** 部门 */
    department?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 姓名 */
    username?: string;
    /** 手机 */
    phone?: string;
    /** 邮件 */
    email?: string;
    /** 工作地点 */
    workPlace?: string;
  };

  type UserArchivesReq = {
    /** 员工ID */
    id?: string;
    /** 证书简历 */
    certificates?: CertificateReq[];
    /** 教育培训 */
    educations?: UserEducationListReq[];
    /** 工作岗位调整记录 */
    jobAdjustments?: UserJobAdjustmentListReq[];
    /** 项目简历 */
    projects?: UserProjectListReq[];
    /** 员工技能 */
    skills?: UserSkillListReq[];
    /** 培训简历 */
    trains?: UserTrainListReq[];
    /** 工作简历 */
    works?: UserWorkListReq[];
  };

  type UserDetailResp = {
    /** ID */
    id?: string;
    /** 姓名 */
    username?: string;
    /** 电话 */
    phone?: string;
    /** 邮件 */
    email?: string;
    /** 权限 */
    permissions?: string[];
    /** 性别-id */
    gender?: string;
    /** 员工类别-id */
    employmentType?: string;
    /** 工作地点 */
    workPlace?: string;
    /** 年龄 */
    age?: string;
    /** 民族-id */
    nation?: string;
    /** 出生日期 */
    dateOfBirth?: string;
    /** 身份证号 */
    personId?: string;
    /** 籍贯 */
    nativePlace?: string;
    /** 最高学历-id */
    topEducation?: string;
    /** 毕业时间 */
    graduationDate?: string;
    /** 毕业院校 */
    graduationSchool?: string;
    /** 政治面貌-id */
    politics?: string;
    /** 婚姻状况-id */
    maritalStatus?: string;
    /** 居住地址 */
    currentAddress?: string;
    /** 紧急联系人 */
    personToBeContacted?: string;
    /** 联系人关系 */
    relation?: string;
    /** 联系人方式1 */
    emergencyPhoneNumber?: string;
    /** 联系人方式2 */
    rgencyPhoneNumber2?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 激活状态 */
    activation?: string;
    /** 部门-id */
    department?: string;
    /** 子部门-id */
    departmentBranch?: string;
    /** 第二部门-id */
    secondDepartment?: string;
    /** 社保缴纳地 */
    socialSecurity?: string;
    /** 职位-id */
    designation?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 直属上级编号 */
    reportsToNumber?: string;
    /** 初始工作时间 */
    firstWorkTime?: string;
    /** 专业 */
    major?: string;
    /** 技术特长 */
    technicalExpertise?: string;
    /** 入职日期 */
    dateOfJoining?: string;
    /** 转正日期 */
    importantTime?: string;
    /** 基本工资 */
    basicSalary?: string;
    /** 福利等级-id */
    grade?: string;
    /** 岗位状态-id */
    workStatus?: string;
    /** 离职日期 */
    relievingDate?: string;
    /** 是应届毕业生 */
    isStu?: string;
    /** 开户行 */
    accountOpening?: string;
    /** 银行账号 */
    bankAcNo?: string;
    /** 劳动合同编号 */
    contractNum?: string;
    /** 合同开始时间 */
    contractStartDate?: string;
    /** 合同结束时间 */
    contractEndDate?: string;
    /** 公司 */
    company?: string;
    /** 参加工作年限 */
    totalWorkYears?: string;
    /** 公司在职年限 */
    rkWorkYears?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 离职申请日期 */
    dimissionApplyDate?: string;
    /** 新工作地点 */
    newWorkAddress?: string;
    /** 离职原因 */
    dimissionReason?: string;
    /** 止薪日期 */
    salaryEndDate?: string;
    /** 实习结束日期 */
    practiceEndDate?: string;
    certificates?: CertificateResp[];
    educations?: UserEducationListReq[];
    jobAdjustments?: UserJobAdjustmentListReq[];
    projects?: UserProjectListReq[];
    skills?: UserSkillListReq[];
    trains?: UserTrainListReq[];
    works?: UserWorkListReq[];
  };

  type UserDropDownReq = {
    /** true 激活 false 全部 */
    activation?: boolean;
    /** 姓名 */
    username?: string;
    /** 部门ID */
    departmentId?: string;
  };

  type UserEducationListReq = {
    /** 学校 */
    schoolName?: string;
    /** 学历 */
    education?: string;
    /** 专业 */
    majorName?: string;
    /** 毕业年份 */
    graduateYear?: string;
    /** 主/选修科目 */
    subjectName?: string;
    /** 是否保存  */
    save?: string;
  };

  type UserInfo = {
    /** ID */
    id?: string;
    /** 姓名 */
    username?: string;
    /** 电话 */
    phone?: string;
    /** 邮件 */
    email?: string;
    /** 编号 */
    employeeNumber?: string;
    /** 部门 */
    department?: string;
    /** 是否实习 */
    ifPractice?: string;
    /** 查看权限 */
    pagePermissions?: string[];
    /** 数据权限 */
    dataPermissions?: string[];
    /** 常用功能 */
    commonlyFunctions?: string[];
  };

  type userInfo1Params = {
    id: string;
  };

  type userInfoParams = {
    req: IdReq;
  };

  type UserInsertReq = {
    /** 姓名 */
    username?: string;
    /** 密码 */
    password: string;
    /** 电话 */
    phone: string;
    /** 邮件 */
    email: string;
    /** 权限 */
    permissions?: string[];
    /** 性别-id */
    gender?: string;
    /** 员工类别-id */
    employmentType?: string;
    /** 工作地点 */
    workPlace?: string;
    /** 年龄 */
    age?: string;
    /** 民族-id */
    nation?: string;
    /** 出生日期 */
    dateOfBirth?: string;
    /** 身份证号 */
    personId?: string;
    /** 籍贯 */
    nativePlace?: string;
    /** 最高学历-id */
    topEducation?: string;
    /** 毕业时间 */
    graduationDate?: string;
    /** 毕业院校 */
    graduationSchool?: string;
    /** 政治面貌-id */
    politics?: string;
    /** 婚姻状况-id */
    maritalStatus?: string;
    /** 居住地址 */
    currentAddress?: string;
    /** 紧急联系人 */
    personToBeContacted?: string;
    /** 联系人关系 */
    relation?: string;
    /** 联系人方式1 */
    emergencyPhoneNumber?: string;
    /** 联系人方式2 */
    rgencyPhoneNumber2?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 激活状态 */
    status?: string;
    /** 部门-id */
    department?: string;
    /** 子部门-id */
    departmentBranch?: string;
    /** 第二部门-id */
    secondDepartment?: string;
    /** 社保缴纳地 */
    socialSecurity?: string;
    /** 职位-id */
    designation?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 直属上级编号 */
    reportsToNumber?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 初始工作时间 */
    firstWorkTime?: string;
    /** 专业 */
    major?: string;
    /** 技术特长 */
    technicalExpertise?: string;
    /** 入职日期 */
    dateOfJoining?: string;
    /** 转正日期 */
    importantTime?: string;
    /** 基本工资 */
    basicSalary?: string;
    /** 福利等级-id */
    grade?: string;
    /** 岗位状态-id */
    workStatus?: string;
    /** 离职日期 */
    relievingDate?: string;
    /** 是应届毕业生 */
    isStu?: string;
    /** 开户行 */
    accountOpening?: string;
    /** 银行账号 */
    bankAcNo?: string;
    /** 劳动合同编号 */
    contractNum?: string;
    /** 合同开始时间 */
    contractStartDate?: string;
    /** 合同结束时间 */
    contractEndDate?: string;
    /** 公司 */
    company?: string;
    /** 参加工作年限 */
    totalWorkYears?: string;
    /** 公司在职年限 */
    rkWorkYears?: string;
    /** 离职申请日期 */
    dimissionApplyDate?: string;
    /** 新工作地点 */
    newWorkAddress?: string;
    /** 离职原因 */
    dimissionReason?: string;
    /** 止薪日期 */
    salaryEndDate?: string;
    /** 实习结束日期 */
    practiceEndDate?: string;
  };

  type UserJobAdjustmentListReq = {
    /** 变更日期 */
    changeTime?: string;
    /** 调整类型 */
    changeType?: string;
    /** 变更内容 */
    changeContent?: string;
    /** 是否保存  */
    save?: string;
  };

  type UserPageResp = {
    /** ID */
    id?: string;
    /** 姓名 */
    username?: string;
    /** 激活(0未激活 1已激活) */
    activation?: number;
    /** 邮箱 */
    email?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 部门 */
    department?: string;
    departmentId?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 工作地点 */
    workPlace?: string;
  };

  type UserProjectListReq = {
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 项目名称 */
    projectName?: string;
    /** 担任岗位 */
    position?: string;
    /** 工作内容 */
    content?: string;
    /** 是否保存  */
    save?: string;
  };

  type UserResetPasswordReq = {
    /** 用户ID */
    id?: string;
    /** 新密码 */
    password?: string;
  };

  type UserSalaryResp = {
    /** 员工主键 */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 性别 */
    sex?: string;
    /** 员工号 */
    employeeNumber?: string;
    /** 部门 */
    department?: string;
    /** 子部门 */
    departmentBranch?: string;
    /** 基本工资 */
    baseSalary?: string;
    /** 其他工资 */
    otherSalary?: string;
    /** 税前工资 */
    preTaxSalary?: string;
    /** 岗位 */
    designation?: string;
    /** 福利等级 */
    benefitLevel?: string;
    benefitLevelId?: string;
    /** 岗位状态 */
    workStatus?: string;
    /** AES密钥 */
    aesKey?: string;
    /** 调整记录 */
    adjustmentRecord?: AdjustmentRecordResp[];
  };

  type UsersByDepartmentIdReq = {
    departmentId?: string;
    departmentGrade?: string;
  };

  type UserScheduleOperationsReq = {
    id?: string;
    /** true确认 false拒绝 */
    operation: boolean;
  };

  type UserScheduleReq = {
    id?: string;
    /** 日程类型（日程/会议） */
    scheduleType?: string;
    /** 参会人 */
    scheduleUserIds?: string[];
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 提醒时间 */
    remindTime?: string[];
    /** 主题 */
    topic?: string;
    /** 全员 */
    whetherFull?: string;
    /** 计划执行时间 */
    dateTime?: string;
    /** 完成状态 */
    completionStatus?: boolean;
    /** 事件描述 */
    description?: string;
  };

  type UserScheduleResp = {
    id?: string;
    userId?: string;
    scheduleType?: string;
    scheduleUserIds?: string[];
    confirmUserIds?: string[];
    refuseUserIds?: string[];
    startTime?: string;
    endTime?: string;
    remindTime?: string[];
    topic?: string;
    whetherFull?: string;
    dateTime?: string;
    completionStatus?: boolean;
    description?: string;
  };

  type UserSkillListReq = {
    /** 技能 */
    skillName?: string;
    /** 是否保存  */
    save?: string;
  };

  type UserTrainListReq = {
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 培训名称 */
    trainingName?: string;
    /** 是否结业 */
    isGraduation?: string;
    /** 是否保存  */
    save?: string;
  };

  type UserUpdateActivationReq = {
    /** ID */
    id?: string;
    /** 状态 0禁用 1激活 */
    activation?: number;
  };

  type UserUpdatePasswordReq = {
    /** 新密码 */
    password?: string;
  };

  type UserUpdateReq = {
    /** ID */
    id: string;
    /** 姓名 */
    username?: string;
    /** 电话 */
    phone?: string;
    /** 邮件 */
    email?: string;
    /** 权限 */
    permissions?: string[];
    /** 性别-id */
    gender?: string;
    /** 员工类别-id */
    employmentType?: string;
    /** 工作地点 */
    workPlace?: string;
    /** 年龄 */
    age?: string;
    /** 民族-id */
    nation?: string;
    /** 出生日期 */
    dateOfBirth?: string;
    /** 身份证号 */
    personId?: string;
    /** 籍贯 */
    nativePlace?: string;
    /** 最高学历-id */
    topEducation?: string;
    /** 毕业时间 */
    graduationDate?: string;
    /** 毕业院校 */
    graduationSchool?: string;
    /** 政治面貌-id */
    politics?: string;
    /** 婚姻状况-id */
    maritalStatus?: string;
    /** 居住地址 */
    currentAddress?: string;
    /** 紧急联系人 */
    personToBeContacted?: string;
    /** 联系人关系 */
    relation?: string;
    /** 联系人方式1 */
    emergencyPhoneNumber?: string;
    /** 联系人方式2 */
    rgencyPhoneNumber2?: string;
    /** 员工编号 */
    employeeNumber?: string;
    /** 激活状态 */
    activation?: string;
    /** 部门-id */
    department?: string;
    /** 子部门-id */
    departmentBranch?: string;
    /** 第二部门-id */
    secondDepartment?: string;
    /** 社保缴纳地 */
    socialSecurity?: string;
    /** 职位-id */
    designation?: string;
    /** 直属上级姓名 */
    reportsToName?: string;
    /** 直属上级编号 */
    reportsToNumber?: string;
    /** 联系电话 */
    cellNumber?: string;
    /** 初始工作时间 */
    firstWorkTime?: string;
    /** 专业 */
    major?: string;
    /** 技术特长 */
    technicalExpertise?: string;
    /** 入职日期 */
    dateOfJoining?: string;
    /** 转正日期 */
    importantTime?: string;
    /** 基本工资 */
    basicSalary?: string;
    /** 福利等级-id */
    grade?: string;
    /** 岗位状态-id */
    workStatus?: string;
    /** 离职日期 */
    relievingDate?: string;
    /** 是应届毕业生 */
    isStu?: string;
    /** 开户行 */
    accountOpening?: string;
    /** 银行账号 */
    bankAcNo?: string;
    /** 劳动合同编号 */
    contractNum?: string;
    /** 合同开始时间 */
    contractStartDate?: string;
    /** 合同结束时间 */
    contractEndDate?: string;
    /** 公司 */
    company?: string;
    /** 参加工作年限 */
    totalWorkYears?: string;
    /** 公司在职年限 */
    rkWorkYears?: string;
    /** 离职申请日期 */
    dimissionApplyDate?: string;
    /** 新工作地点 */
    newWorkAddress?: string;
    /** 离职原因 */
    dimissionReason?: string;
    /** 止薪日期 */
    salaryEndDate?: string;
    /** 实习结束日期 */
    practiceEndDate?: string;
  };

  type UserViewRulesReq = {
    /** 制度主键 */
    rulesId?: string;
    /** 制度名称 */
    rulesName?: string;
    /** 是否确认 */
    confirmBool?: boolean;
    /** 异议 */
    opinion?: string;
  };

  type UserViewRulesResp = {
    /** 主键 */
    id?: string;
    /** 制度主键 */
    rulesId?: string;
    /** 制度名称 */
    rulesName?: string;
    /** 用户主键 */
    userId?: string;
    /** 用户名称 */
    userName?: string;
    /** 是否有异议 */
    opinionBool?: boolean;
    /** 是否确认 */
    confirmBool?: boolean;
    /** 异议 */
    opinion?: string;
    /** 首次查看时间 */
    viewTime?: string;
    /** 确认时间 */
    confirmTime?: string;
  };

  type UserWorkListReq = {
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 公司 */
    company?: string;
    /** 职位 */
    position?: string;
    /** 工作内容 */
    content?: string;
    /** 是否保存  */
    save?: string;
  };

  type WeekAnalysisDetailsResp = {
    /** ID */
    id?: string;
    /** 工作日 */
    weekday?: number;
    /** 编号 */
    documentNumber?: string;
    /** 姓名 */
    employeeName?: string;
    /** 总工时 */
    totalHours?: number;
    /** NB工时 */
    nb?: number;
    /** SH工时 */
    sh?: number;
    /** SQ工时 */
    sq?: number;
    /** 其他工时 */
    other?: number;
    /** 序号 */
    rn?: number;
    /** 部门 */
    departmentId?: string;
    department?: string;
    /** 子部门 */
    departmentBranchId?: string;
    departmentBranch?: string;
  };

  type WeekAnalysisResp = {
    /** 总条数 */
    total?: number;
    /** 总页数 */
    pages?: number;
    /** 数据 */
    records?: WeekAnalysisDetailsResp[];
    /** 当前分页数 */
    size?: number;
    /** 当前页 */
    current?: number;
    /** 工作日 */
    weekday?: number;
    /** 应提交数 */
    shouldSubmit?: number;
    /** 已提交数 */
    alreadySubmit?: number;
    /** 未提交数 */
    notSubmit?: number;
    /** 加班人数 */
    overTimeNumber?: number;
    /** 最长工时 */
    maxHours?: number;
    /** 工时不足 */
    lackHours?: number;
    /** 最少工时 */
    minHours?: number;
    /** 统计数据 */
    statistics?: WeekAnalysisStatisticsResp[];
  };

  type WeekAnalysisStatisticsResp = {
    /** 年份 */
    yearWeek?: string;
    /** 总合 */
    total?: number;
    /** 平均 */
    average?: number;
  };

  type WeekDetailsInfoResp = {
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 日期 */
    recordDate?: string;
    /** 星期 */
    recordWeek?: string;
    /** 执行人 */
    employeeName?: string;
    /** 内容 */
    content?: string;
    /** 正常工时 */
    workTime?: number;
    /** 其他时间段工时 */
    workOtherTime?: string;
    /** 工作类型 */
    workType?: string;
    /** 是否自动生成 */
    ifDefault?: number;
  };

  type WeekDetailsInsertReq = {
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 日期 */
    recordDate?: string;
    /** 星期 */
    recordWeek?: string;
    /** 执行人 */
    employeeName?: string;
    /** 内容 */
    content?: string;
    /** 正常工时 */
    workTime?: number;
    /** 其他时间段工时 */
    workOtherTime?: string;
    /** 工作类型 */
    workType?: string;
  };

  type WeekExportReq = {
    /** 开始日期 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
  };

  type WeekInfoFeesResp = {
    /** 主键 */
    id?: string;
    /** 周报id */
    weekId?: string;
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 日期 */
    recordDate?: string;
    /** 部门 */
    employeeDepartment?: string;
    /** 执行人 */
    employeeName?: string;
    /** 内容 */
    content?: string;
    /** 正常工时 */
    workTime?: number;
    /** 工作类型 */
    workType?: string;
    /** 成本 */
    cost?: number;
    /** 成本状态 0：未关联 1：当前合同关联 2：其他合同关联 */
    costStatus?: string;
  };

  type weekInfoParams = {
    req: IdReq;
  };

  type WeekInfoResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 汇总人ID */
    employeeId?: string;
    /** 汇总人 */
    employeeName?: string;
    /** 员工部门 */
    employeeDepartment?: string;
    /** 开始日期 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
    /** 状态 */
    status?: string;
    /** 总工时 */
    countTime?: number;
    /** 工作内容 */
    weekDetailsList?: WeekDetailsInfoResp[];
    /** 下周工作计划 */
    nextWeeks?: NextWeek[];
  };

  type WeekInsertReq = {
    /** 汇总人 */
    employeeName?: string;
    /** 开始日期 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
    /** 总工时 */
    countTime?: number;
    /** 工作内容 */
    weekDetailsList?: WeekDetailsInsertReq[];
    /** 下周工作计划 */
    nextWeeks?: NextWeek[];
  };

  type WeekPageResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 汇总人ID */
    employeeId?: string;
    /** 汇总人 */
    employeeName?: string;
    /** 开始日期 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
    /** 状态 */
    status?: string;
    /** 总工时 */
    countTime?: number;
  };

  type WeekStatusReq = {
    /** ID */
    id?: string;
    /** 状态 */
    status?: string;
  };

  type WeekUpdateReq = {
    /** ID */
    id?: string;
    /** 汇总人 */
    employeeName?: string;
    /** 开始日期 */
    startTime?: string;
    /** 结束日期 */
    endTime?: string;
    /** 总工时 */
    countTime?: number;
    /** 工作内容 */
    weekDetailsList?: WeekDetailsInsertReq[];
    /** 下周工作计划 */
    nextWeeks?: NextWeek[];
  };

  type WorkFlowRankReq = {
    /** 流程类型 */
    type?: string;
  };

  type WorkFlowTypeReq = {
    /** 流程类型 */
    type?: string;
    /** 页数 */
    pageNum?: number;
    /** 每页多少条 */
    pageSize?: number;
    /** 编号模糊查询 */
    searchNumber?: string;
    /** 申请人模糊查询 */
    searchStartUsername?: string;
    /** 流程状态筛选 */
    filterActiviStatus?: string;
    /** 是否代办 0 全部 1 代办 */
    pendingApproval?: number;
  };

  type workOrderInfoParams = {
    req: IdReq;
  };

  type WorkOrderInfoResp = {
    /** ID */
    id?: string;
    /** 工单编号 */
    documentNumber?: string;
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 工单类型 */
    type?: string;
    /** 工程师工号 */
    employeeNumber?: string;
    /** 工程师姓名 */
    employeeName?: string;
    /** 联系人姓名 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 工时 */
    workHours?: string;
    /** 事件描述 */
    describe?: string;
    /** 工作内容 */
    content?: string;
    /** 下一步计划 */
    nextPlan?: string;
    /** 审批状态 */
    activiStatus?: string;
    /** 文件地址 */
    fileUrl?: string[];
    /** 文件地址 */
    wechatFileUrl?: string[];
    /** 文件名称 */
    fileName?: string[];
  };

  type WorkOrderInsertReq = {
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 工单类型 */
    type?: string;
    /** 工程师工号 */
    employeeNumber?: string;
    /** 工程师姓名 */
    employeeName?: string;
    /** 联系人姓名 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 工时 */
    workHours?: string;
    /** 事件描述 */
    describe?: string;
    /** 工作内容 */
    content?: string;
    /** 下一步计划 */
    nextPlan?: string;
    /** 文件地址 */
    fileUrl?: string[];
  };

  type WorkOrderPageResp = {
    /** ID */
    id?: string;
    /** 单据编号 */
    documentNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 工程师姓名 */
    employeeName?: string;
    /** 联系人姓名 */
    contactName?: string;
    /** 事件描述 */
    describe?: string;
    /** 工时 */
    workHours?: string;
    /** 审批状态 */
    activiStatus?: string;
  };

  type WorkOrderUpdateReq = {
    /** ID */
    id?: string;
    /** 项目id */
    projectId?: string;
    /** 项目编号 */
    projectNumber?: string;
    /** 项目名称 */
    projectName?: string;
    /** 项目类型 */
    projectType?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 工单类型 */
    type?: string;
    /** 工程师工号 */
    employeeNumber?: string;
    /** 工程师姓名 */
    employeeName?: string;
    /** 联系人姓名 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 工时 */
    workHours?: string;
    /** 事件描述 */
    describe?: string;
    /** 工作内容 */
    content?: string;
    /** 下一步计划 */
    nextPlan?: string;
    /** 文件地址 */
    fileUrl?: string[];
  };

  type WXAuthReq = {
    /** 微信code */
    code: string;
    /** 手机号 */
    phoneCode?: string;
  };

  type WXRefReq = {
    /** ref token */
    refreshAuthorization?: string;
  };
}
