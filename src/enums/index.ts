// 清理条件
export const ROLE = [
  { label: '开发', value: 'developer' },
  { label: '测试', value: 'tester' },
  { label: '管理员', value: 'master' },
];

// 用户状态
export const USER_STATUS = [
  { label: '激活', value: 1, status: 'success' },
  { label: '未激活', value: 0, status: 'error' },
];
//员工状态
export const EMPLOYEE_STATUS = [
  { label: '激活', value: 1, status: 'success' },
  { label: '失效', value: 0, status: 'error' },
];

// 通用的审批状态
export const APPROVAL_STATUS = [
  { label: '待提交', value: '0', status: 'default' },
  { label: '审核中', value: '1', status: 'processing' },
  { label: '通过', value: '2', status: 'success' },
  { label: '拒绝', value: '3', status: 'error' },
  { label: '驳回', value: '4', status: 'error' },
];

//项目状态
export const PROJECT_STATUS = [
  { label: '正常', value: 'NORMAL', status: 'success' },
  { label: '关闭', value: 'CLOSED', status: 'error' },
];

// 合同性质

export const CONTRACT_CHARACTER = [
  { label: '直签', value: 'DS', textColor: 'green' },
  { label: '分包', value: 'SC', textColor: 'cyan' },
];

// 合同类别
export const CONTRACT_CATEGORY = [
  { value: 'OTH', label: '其他类' },
  { value: 'TCH', label: '技术服务类' },
  { value: 'INT', label: '系统集成类' },
  { value: 'DEV', label: '软件开发类' },
  { value: 'SAL', label: '销售类' },
];
// 合同类别-采购
export const CONTRACT_CATEGORY_P = [{ value: 'PC', label: '采购类' }];

// 合同服务类别
export const CONTRACT_SERVICES_CATEGORY = [
  { value: 'AGSE', label: '协议服务' },
  { value: 'ONCO', label: '单次咨询' },
  { value: 'ONTR', label: '单次培训' },
  { value: 'ONDE', label: '单次开发' },
  { value: 'ONMI', label: '单次迁移' },
  { value: 'ONSA', label: '单次销售' },
  { value: 'ONIN', label: '单次集成' },
  { value: 'ANMA', label: '年度维保' },
  { value: 'ANSA', label: '年度销售' },
  { value: 'ANIN', label: '年度集成' },
];

// 合同服务类别-采购合同
export const CONTRACT_SERVICES_CATEGORY_P = [
  { value: '1', label: '中标服务费' },
  { value: '2', label: '其他类' },
  { value: '3', label: '培训类' },
  { value: '4', label: '技术服务类' },
  { value: '5', label: '系统集成类' },
  { value: '6', label: '软件开发类' },
  { value: '7', label: '采购类' },
];
// 合同服务类别-内部
export const CONTRACT_SERVICES_CATEGORY_I = [
  { value: '1', label: '会员费' },
  { value: '2', label: '内部设备' },
  { value: '3', label: '员工福利' },
  { value: '4', label: '外宣相关' },
  { value: '5', label: '委托服务' },
  { value: '6', label: '房租物业' },
  { value: '7', label: '行政费用' },
  { value: '8', label: '财务报表类' },
  { value: '9', label: '资质审核类' },
  { value: '10', label: '软件开发' },
];

// 甲乙方类型
export const CUSTOMER_TYPE = [
  { value: 'CLIENT', label: '客户' },
  { value: 'INSTITUTION', label: '招标机构' },
  { value: 'VENDOR', label: '供应商' },
];

// 行业
export const INDUSTRY = [
  { value: 'EN', label: '企业' },
  { value: 'IN', label: '保险' },
  { value: 'MI', label: '军工' },
  { value: 'ME', label: '医疗' },
  { value: 'GE', label: '地质' },
  { value: 'BR', label: '广电' },
  { value: 'GO', label: '政府' },
  { value: 'ED', label: '教育' },
  { value: 'WC', label: '水利' },
  { value: 'WT', label: '水路交通' },
  { value: 'CO', label: '煤炭' },
  { value: 'EL', label: '电力' },
  { value: 'SC', label: '科研院校' },
  { value: 'SF', label: '航空' },
  { value: 'COM', label: '通讯' },
  { value: 'FI', label: '金融' },
];

// 签订状态
export const SIGN_STATUS = [
  { value: 'SIGNED', label: '签订', status: 'Success' },
  { value: 'TIMEOUT', label: '走流程中', status: 'Processing' },
  { value: 'NOCONTRACT', label: '无合同', status: 'Default' },
  { value: 'WBWSB', label: '中标待签', status: 'Warning' },
];

// 合同状态
export const CONTRACT_STATUS = [
  { value: 'EXECUTING', label: '执行中', status: 'Processing' },
  { value: 'PROCESSING', label: '暂停', status: 'Warning' },
  { value: 'STOP', label: '终止', status: 'Error' },
  { value: 'END', label: '结束', status: 'Default' },
];

// 协议类别
export const AGREEMENT_CATEGORY = [{ value: '1', label: '与供应商或其他公司单位建立的合作关系' }];
// 协议类别
export const AGREEMENT_TYPE = [
  { value: 'PARTNER', label: '合作伙伴' },
  { value: 'FAE', label: '技术支持' },
  { value: 'WKA', label: '经销商' },
];
// 状态
export const AGREEMENT_STATUS = [
  { value: 'NOT_SIGNED', label: '待签订', status: 'Processing' },
  { value: 'SIGNED', label: '已签订', status: 'Success' },
  { value: 'EXECUTING', label: '执行中', status: 'Processing' },
  { value: 'END', label: '已结束', status: 'Default' },
  { value: 'CANCEL', label: '已取消', status: 'Default' },
  { value: 'UPGRADED', label: '协议已升级', status: 'Success' },
];

// 合作方式
export const COOPERATION_WAYS = [
  { value: 'SELF_OP', label: '自营' },
  { value: 'COOP', label: '合作' },
];

// 区域
export const REGION = [
  {
    label: '东北',
    value: 'DB',
  },
  {
    label: '华东',
    value: 'HD',
  },
  {
    label: '华中',
    value: 'HZ',
  },
  {
    label: '华北',
    value: 'HB',
  },
  {
    label: '华南',
    value: 'HN',
  },
  {
    label: '西北',
    value: 'XB',
  },
  {
    label: '西南',
    value: 'XN',
  },
];
export const PROVINCES = [
  {
    label: '北京市',
    value: '36137',
  },
  {
    label: '天津市',
    value: '33337',
  },
  {
    label: '河北省',
    value: '46809',
  },
  {
    label: '山西省',
    value: '16535',
  },
  {
    label: '内蒙古自治区',
    value: '20012',
  },
  {
    label: '辽宁省',
    value: '26668',
  },
  {
    label: '吉林省',
    value: '14776',
  },
  {
    label: '黑龙江省',
    value: '19253',
  },
  {
    label: '上海市',
    value: '41182',
  },
  {
    label: '江苏省',
    value: '85869',
  },
  {
    label: '浙江省',
    value: '62337',
  },
  {
    label: '安徽省',
    value: '36578',
  },
  {
    label: '福建省',
    value: '35804',
  },
  {
    label: '江西省',
    value: '32708',
  },
  {
    label: '山东省',
    value: '72634',
  },
  {
    label: '河南省',
    value: '61384',
  },
  {
    label: '湖北省',
    value: '48396',
  },
  {
    label: '湖南省',
    value: '42768',
  },
  {
    label: '广东省',
    value: '97277',
  },
  {
    label: '广西壮族自治区',
    value: '32725',
  },
  {
    label: '海南省',
    value: '20321',
  },
  {
    label: '重庆市',
    value: '37661',
  },
  {
    label: '四川省',
    value: '50665',
  },
  {
    label: '贵州省',
    value: '22988',
  },
  {
    label: '云南省',
    value: '28968',
  },
  {
    label: '西藏自治区',
    value: '1302',
  },
  {
    label: '陕西省',
    value: '32385',
  },
  {
    label: '甘肃省',
    value: '11688',
  },
  {
    label: '青海省',
    value: '9195',
  },
  {
    label: '宁夏回族自治区',
    value: '8500',
  },
  {
    label: '新疆维吾尔自治区',
    value: '20288',
  },
];

// 支付、收款方式
export const PAY_CONDITION = [
  {
    label: '对公',
    value: 'FP',
  },
];

// 合同收款状态信息

export const COLLECTION_STATUS = [
  {
    label: '未开票未收款',
    value: 'NTNC',
  },
  {
    label: '未开票已收款',
    value: 'NTHC',
  },
  {
    label: '已开票未收款',
    value: 'HTNC',
  },
  {
    label: '已开票已收款',
    value: 'HTHC',
  },
];

// 开票状态

export const TICKET_STATUS = [
  {
    label: '未开票',
    value: 'NOT_TICKET',
    status: 'default',
  },
  {
    label: '已开票',
    value: 'TICKET',
    status: 'success',
  },
];

// 发票类型
export const INVOICE_TYPE = [
  {
    label: '专票',
    value: 'ST',
  },
  {
    label: '普票',
    value: 'NT',
  },
];

// 收款方式、付款方式
export const PAYMENT = [
  {
    label: '对公',
    value: 'FP',
  },
  {
    label: '现金',
    value: 'FA',
  },
  {
    label: '转账支票',
    value: 'FC',
  },
];

export const PAYMENT_STATUS = [
  {
    label: '未付款',
    value: 'NOT_PAY',
    status: 'warning',
  },
  {
    label: '已付款',
    value: 'PAY',
    status: 'success',
  },
  {
    label: '已取消',
    value: 'CANCELED',
    status: 'error',
  },
];

// 周报状态
export const WEEKLY_REPORT_STATUS = [
  {
    label: '草稿',
    value: 'DRAFT',
    status: 'warning',
  },
  {
    label: '已提交',
    value: 'SUBMITTED',
    status: 'success',
  },
  {
    label: '过期',
    value: 'OVERDUE',
    status: 'error',
  },
];
//  工作类型
export const WORK_TYPE = [
  {
    label: '现场服务',
    value: '现场服务',
  },

  {
    label: '驻场服务',
    value: '驻场服务',
  },
  {
    label: '驻场二线',
    value: '驻场二线',
  },

  {
    label: '远程支持',
    value: '远程支持',
  },
  {
    label: '值守保障',
    value: '值守保障',
  },
  {
    label: '软件开发',
    value: '软件开发',
  },
  {
    label: '测试验证',
    value: '测试验证',
  },
  {
    label: '自我学习',
    value: '自我学习',
  },
  {
    label: '售前支持',
    value: '售前支持',
  },
  {
    label: '电话咨询',
    value: '电话咨询',
  },
  {
    label: '文档编写',
    value: '文档编写',
  },
  {
    label: '工作会议',
    value: '工作会议',
  },
  {
    label: '客户拜访',
    value: '客户拜访',
  },
  {
    label: '人员招聘',
    value: '人员招聘',
  },
  {
    label: '人员协调',
    value: '人员协调',
  },
  {
    label: '售前支持',
    value: '售前支持',
  },
  {
    label: '日常管理',
    value: '日常管理',
  },
  {
    label: '认证培训',
    value: '认证培训',
  },
  {
    label: '休假',
    value: '休假',
  },
  {
    label: '病假',
    value: '病假',
  },
  {
    label: '其他',
    value: '其他',
  },
];

// 认领状态
export const CLAIM_STATUS = [
  {
    label: '未认领',
    value: 'NOT_CLAIM',
    status: 'warning',
  },
  {
    label: '已认领',
    value: 'CLAIM',
    status: 'success',
  },
];

// 收款类型
export const COLLECTION_TYPE = [
  {
    label: '合同款',
    value: 'CONTRACT',
  },
  {
    label: '保证金',
    value: 'MARGIN',
  },
];

export const PAYMENT_TYPE = [
  {
    label: '合同款',
    value: 'GOODS',
  },
  {
    label: '通用付款',
    value: 'GENERAL',
  },
  {
    label: '投标保证金',
    value: 'COLLATERAL',
  },
  {
    label: '押金',
    value: 'DEPOSIT',
  },
  {
    label: '履约保证金',
    value: 'PERFORMANCE',
  },
];
export const COLLECT_STATUS = [
  {
    label: '未收款',
    value: 'NOT_COLLECT',
    status: 'warning',
  },
  {
    label: '已收款',
    value: 'COLLECT',
    status: 'success',
  },
];

export const WRITE_OFF_STATUS = [
  {
    label: '未销账',
    value: 'NOT_LOGOFF',
    status: 'warning',
  },
  {
    label: '已销账',
    value: 'LOGOFF',
    status: 'success',
  },
];
// 合同类型
export const CONTRACT_TYPE = [
  {
    label: '采购合同',
    value: 'PC',
  },
  {
    label: '内部合同',
    value: 'IC',
  },
];
// 通用状态
export const STATUS = [
  {
    label: '启用',
    value: '1',
    status: 'success',
  },
  {
    label: '禁用',
    value: '0',
    status: 'error',
  },
];

// 过期状态

export const EXPIRED_STATE = [
  {
    label: '有效',
    value: '0',
    status: 'success',
  },
  {
    label: '已过期',
    value: '1',
    status: 'error',
  },
];

export const PROJECT_PROGRESS = [
  {
    label: 'funnel 20%',
    value: 'funnel',
  },
  {
    label: 'upside 50%',
    value: 'upside',
  },
  {
    label: 'focus 80%',
    value: 'focus',
  },
  {
    label: 'commit 100%',
    value: 'commit',
  },
  {
    label: 'pending',
    value: 'pending',
  },
  {
    label: 'lost 0%',
    value: 'lost',
  },
  {
    label: 'booked 0%',
    value: 'booked',
  },
];
//* 客户-销售信息计划表中项目类型的枚举值
export const PROJECT_BRAND = [
  {
    label: '技术服务',
    value: 'TECHNICAL_SERVICE',
  },
  {
    label: '自主软件产品',
    value: 'SELF_OWNED_SOFTWARE',
  },
  {
    label: 'IT采购',
    value: 'IT_PURCHASE',
  },
  {
    label: '软件开发服务',
    value: 'SOFTWARE_DEVELOPMENT_SERVICE',
  },
];

//是否带薪休假
export const PAID_VACATION_VALUE = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  },
];

// 制度管理状态
export const SYSTEM_STATUS = [
  { label: '公示中', value: 'processing', status: 'processing' },
  { label: '生效', value: 'success', status: 'success' },
  { label: '失效', value: 'warning', status: 'warning' },
  { label: '作废', value: 'error', status: 'error' },
];

//资源使用申请-借用状态
export const BORROW_STATUS = [
  { label: '已归还', value: 'RETURNED' },
  { label: '借用中', value: 'BORROWING' },
  { label: '未借用', value: 'NOT_BORROWED' },
];

//资质类型
export const QUALIFICATION_TYPE = [
  { label: '企业', value: 'COMPANY' },
  { label: '软著', value: 'SOFTWARE_COPYRIGHT' },
  { label: '合作伙伴', value: 'PARTNER' },
];

// 项目类型
export const PROJECT_TYPE = [
  { label: '内部项目', value: 'NB' },
  { label: '销售项目', value: 'XS' },
  { label: '售前项目', value: 'SQ' },
  { label: '售后项目', value: 'SH' },
  { label: '开发项目', value: 'KF' },
];

// 客户级别
export const CUSTOMER_LEVEL = [
  { label: '一级', value: 1 },
  { label: '二级', value: 2 },
  { label: '三级', value: 3 },
];

// 返还状态
export const RESTITUTION_STATUS = [
  { label: '进行中', value: 'EXECUTING', status: 'processing' },
  { label: '已结束', value: 'CLOSED', status: 'success' },
  { label: '停止返还', value: 'TIME_OUT', status: 'default' },
];

// 日程类型
export const SCHEDULE_TYPE = [
  { label: '日程', value: 'SCHEDULE' },
  { label: '会议', value: 'MEET' },
];

//日程提醒
export const SCHEDULE_REMINDS = [
  {
    label: '会议开始时',
    value: 'WHEN_START',
  },
  {
    label: '5分钟前',
    value: 'BEFORE_FIVE_MIN',
  },
  {
    label: '15分钟前',
    value: 'BEFORE_FIFTEEN_MIN',
  },
  {
    label: '一小时前',
    value: 'BEFORE_ONE_HOUR',
  },
  {
    label: '一天前',
    value: 'BEFORE_ONE_DAY',
  },
];

//日程状态筛选
export const SCHEDULE_FILTER = [
  {
    label: '待我确认',
    value: 'WAIT_CONFIRMED',
    key: 'WAIT_CONFIRMED',
  },
  {
    label: '我参与的',
    value: 'CONFIRMED',
    key: 'CONFIRMED',
  },
  {
    label: '我创建的',
    value: 'CREATED',
    key: 'CREATED',
  },
  {
    label: '全部',
    value: 'ALL',
    key: 'ALL',
  },
];

// 采购合同 & 内部下单 & 合同付款详情中的付款计划详细信息表的付款状态
export const PAY_STATUS = [
  {
    label: '已收票已付款',
    value: 'REAP',
  },
  {
    label: '已收票未付款',
    value: 'RENP',
  },
  {
    label: '未收票未付款',
    value: 'NRENP',
  },
  {
    label: '未收票已付款',
    value: 'NREP',
  },
];

// 业绩考核初始表状态
export const PERFORMANCE_STATUS = [
  {
    label: '未核算',
    value: 'NOT_ACCOUNT',
    status: 'default',
  },
  {
    label: '核算中',
    value: 'ACCOUNTING',
    status: 'processing',
  },
  {
    label: '核算完',
    value: 'BEEN_ACCOUNTED',
    status: 'warning',
  },
  {
    label: '已确认',
    value: 'ACCOUNT_CONFIRMED',
    status: 'success',
  },
  {
    label: '已拒绝',
    value: 'ACCOUNT_REJECTED',
    status: 'error',
  },
];

export const PERFORMANCE_PRINT_STATUS = [
  {
    label: '未提交',
    value: 'PER_NOT_SUBMIT',
    status: 'default',
  },
  {
    label: '已提交',
    value: 'PER_SUBMIT',
    status: 'processing',
  },
  {
    label: '已确认',
    value: 'PER_CONFIRM',
    status: 'success',
  },
];

// 任务状态
export const TASK_STATUS = [
  {
    label: '未开始',
    value: 'NOT_STARTED',
    status: 'default',
  },
  {
    label: '执行中',
    value: 'IN_PROGRESS',
    status: 'processing',
  },
  {
    label: '已终止',
    value: 'TERMINATED',
    status: 'default',
  },
  {
    label: '已提交',
    value: 'SUBMITTED',
    status: 'warning',
  },
  {
    label: '按期完成',
    value: 'COMPLETED_ON_TIME',
    status: 'success',
  },
  {
    label: '逾期完成',
    value: 'COMPLETED_OVERDUE',
    status: 'error',
  },
];

// 核算标识
export const ACCOUNTING_MARK = [
  {
    label: '已核算',
    value: '1',
  },
  {
    label: '未核算',
    value: '0',
  },
];

// 票据类型
export const BILL_TYPE = [
  {
    label: '电子票',
    value: 'ELECTRONIC',
  },
  {
    label: '纸质票',
    value: 'PAPER',
  },
];

// 工资变更原因
export const SALARY_CHANGE_REASON = [
  {
    label: '入职',
    value: '1',
  },
  {
    label: '转正',
    value: '2',
  },
  {
    label: '调薪',
    value: '3',
  },
];

// 工资调整信息状态
export const SALARY_CHANGE_STATUS = [
  {
    label: '正常',
    value: 'NORMAL',
  },
  {
    label: '已作废',
    value: 'CANCEL',
  },
];

// 成本状态
export const COST_STATUS = [
  {
    label: '未关联',
    value: '0',
  },
  {
    label: '当前合同关联',
    value: '1',
    color: 'cyan',
  },
  {
    label: '其他合同关联',
    value: '2',
    color: 'volcano',
  },
];

// 是否创建售前项目
export const IS_CREATE_PROJECT = [
  {
    label: '创建',
    value: '1',
  },
  {
    label: '不创建',
    value: '0',
  },
];
