import { PAYMENT } from '@/enums';
import { pageRefund } from '@/services/oa/conInvoiced';
import { logoff } from '@/services/oa/paymentApplication';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  DrawerForm,
  DrawerFormProps,
  ProColumns,
  ProFormInstance,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { message, Space } from 'antd';
import React, { useRef, useState } from 'react';

const CollectionDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.RefundResp[]>([]);

  setTimeout(() => {
    formRef.current?.setFieldsValue(initialValues);
  }, 100);

  // 表格
  const columns: ProColumns<API.RefundResp>[] = [
    {
      title: '退款记录号',
      dataIndex: 'refundNumber',
      width: 200,
      fixed: 'left',
      render: (text, record) => (
        <a className="rk-a-span" onClick={() => history.push(`/finance/JR/backs/${record.id}`)}>
          {text}
        </a>
      ),
    },
    {
      title: '退款日期',
      dataIndex: 'refundTime',
      valueType: 'dateTime',
    },

    {
      title: '退款金额',
      dataIndex: 'refAmount',
      valueType: 'money',
    },
    {
      title: '退款方式',
      dataIndex: 'payWay',
      valueEnum: option2enum(PAYMENT),
    },
    {
      title: '认领人',
      dataIndex: 'applicationUser',
      width: 100,
      ellipsis: true,
    },
    {
      title: '对方单位名称(账户名)',
      dataIndex: 'institutionName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '对方开户银行',
      dataIndex: 'bank',
      width: 120,
      ellipsis: true,
    },
    {
      title: '对方银行账号',
      dataIndex: 'account',
      width: 150,
      ellipsis: true,
    },
    {
      title: '收款记录号',
      dataIndex: 'collectNumber',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/JR/receipt-list/details/${entity.receiptId}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '付款记录号',
      dataIndex: 'payNumber',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/JR/payment-list/${entity.paymentId}`)}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '申请编号',
      dataIndex: 'marginNumber',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => history.push(`/finance/payment/general/details/${entity.marginId}`)}
          >
            {dom}
          </a>
        );
      },
    },
  ];

  return (
    <DrawerForm<any>
      title="销账"
      width={860}
      formRef={formRef}
      open={open}
      onOpenChange={(visible) => {
        onOpenChange?.(visible);
      }}
      submitter={{
        searchConfig: {
          submitText: '确定',
        },
      }}
      onFinish={async (value) => {
        const refundIds = selectedRows?.map((item) => item.id!); //退款记录Ids
        const res = await logoff({
          payId: value?.id,
          refundIds: refundIds,
        });
        const success = res.code === 200;
        if (success) {
          message.success('销账成功!');
          onFinish?.(value);
          return success;
        }
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" />
        <ProFormText name="payMoney" label="payMoney" placeholder="请输入" />
      </div>
      <ProTable<API.RefundResp>
        className="inner-table"
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="退款记录表"
        params={{ id: initialValues?.id }}
        request={async () => {
          const res = await pageRefund({
            pageNum: 1,
            pageSize: 10000,
          });
          return {
            data: res?.data?.records?.filter((item) => item.refundTag === 'NOT_LOGOFF') || [],
            success: true,
          };
        }}
        tableAlertRender={({ selectedRows }) => {
          return (
            <Space size={24}>
              <span>已选 {selectedRows.length} 项</span>
              <span>
                退款总金额:
                {selectedRows.reduce((pre, item) => {
                  return pre + (Number(item.refAmount) || 0);
                }, 0)}
                元
              </span>
            </Space>
          );
        }}
      />
    </DrawerForm>
  );
};

export default CollectionDrawerForm;
