import { APPROVAL_STATUS } from '@/enums';
import { approvalList } from '@/pages/Approval/Dashboard';
import { treatSubmit } from '@/services/oa/home';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useLocation } from '@umijs/max';
import { Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';

//待提交时跳转详情路由
export const list = [
  {
    title: '休假申请',
    path: '/human-resources/leave-application',
    key: 'LEAVE_APPROVAL_PROCESS',
  },
  {
    title: '销假申请',
    path: '/human-resources/leave-sell',
    key: 'REVOCATION_APPROVAL',
  },
  {
    title: '资源使用申请',
    path: '/human-resources/resource-application',
    key: 'RESOURCE_APPLICATION',
  },
  {
    title: '招聘申请',
    path: '/human-resources/recruitment-application/details',
    key: 'RECRUITMENT_APPLICATION',
  },
  {
    title: '客户',
    path: '/crm/customer/edit',
    key: 'BUSINESS_PARTNER-CUST',
  },
  {
    title: '招标机构',
    path: '/crm/institution/edit',
    key: 'BUSINESS_PARTNER-INST',
  },
  {
    title: '供应商',
    path: '/crm/suppliers/edit',
    key: 'BUSINESS_PARTNER-SUPP',
  },
  {
    title: '报销列表',
    path: '/finance/reimbursement/list/edit',
    key: 'REIMBURSEMENT_APPROVAL',
  },
  {
    title: '培训报销',
    path: '/finance/reimbursement/training',
    key: 'TRAINING_REIMBURSEMENT_APPROVAL',
  },
  {
    title: '项目付款申请',
    path: '/finance/payment/general/details',
    key: 'UNIVERSAL_PAYMENT',
  },
  {
    title: '合同付款申请',
    path: '/finance/payment/contract',
    key: 'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION',
  },
  {
    title: '售后项目',
    path: '/project/after-sales/edit',
    key: 'PROJECT_APPROVAL_SH',
  },
  {
    title: '售前项目',
    path: '/project/pre-sales/edit',
    key: 'PROJECT_APPROVAL_SQ',
  },
  {
    title: '销售项目',
    path: '/project/sales/edit',
    key: 'PROJECT_APPROVAL_XS',
  },
  {
    title: '内部项目',
    path: '/project/internal/edit',
    key: 'PROJECT_APPROVAL_NB',
  },
  {
    title: '开发项目',
    path: '/project/develop/edit',
    key: 'PROJECT_APPROVAL_KF',
  },
  {
    title: '工单管理',
    path: '/work-orders/edit',
    key: 'WORK_ORDER_APPROVAL',
  },
  {
    title: '主合同审批',
    path: '/contract/main/edit',
    key: 'MAIN_CONTRACT_APPROVAL',
  },
  {
    title: '采购合同审批',
    path: '/contract/purchase/edit',
    key: 'PURCHASE_CONTRACT_APPROVAL',
  },
  {
    title: '内部合同审批',
    path: '/contract/internal/edit',
    key: 'INTERNAL_CONTRACT_APPROVAL',
  },
  {
    title: '公告审批',
    path: '/human-resources/announcement',
    key: 'ANNOUNCEMENT_APPROVAL',
  },
];
//合并审批路由
const detailAndApprovalList = list.map((item) => ({
  ...item,
  approvalPath: item.key.includes('BUSINESS_PARTNER')
    ? '/approval/partner'
    : approvalList.find((i) => i.key === item.key)?.path,
}));

const submitType = detailAndApprovalList?.map((item) => ({
  ...item,
  label: item.title,
  value: item.key,
}));

//来源枚举
const options = list?.map((item) => ({
  ...item,
  label: item.key?.includes('BUSINESS_PARTNER') === true ? '合作伙伴' : item.title,
  value: item.key?.includes('BUSINESS_PARTNER') === true ? 'BUSINESS_PARTNER' : item?.key,
}));

//表格的菜单枚举
const menuStatus = [...APPROVAL_STATUS.map((item) => ({ ...item, key: item.value }))];

const PendingSubmit: React.FC = () => {
  // 解析URL查询参数
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentKey = queryParams.get('activiStatus') || '0';
  const [activeKey, setActiveKey] = useState<string>(currentKey);

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '编号',
      dataIndex: 'DOCUMENT_NUMBER',
      width: 150,
      copyable: true,
      render(dom, entity) {
        const { newType, ID, ACTIVI_STATUS, SOURCE_TABLE } = entity;
        const app = submitType.find((item) => item.key === newType);
        let url: string;
        if (ACTIVI_STATUS === '0') {
          //跳转详情页
          if (
            [
              'LEAVE_APPROVAL_PROCESS',
              'REVOCATION_APPROVAL',
              'RESOURCE_APPLICATION',
              'TRAINING_REIMBURSEMENT_APPROVAL',
              'ANNOUNCEMENT_APPROVAL',
            ].includes(newType)
          ) {
            url = `${app?.path}?current=1&pageSize=10&activiStatus=${ACTIVI_STATUS}`;
          } else {
            url = `${app?.path}/${ID}`;
          }
        } else {
          //跳转审批页
          if (SOURCE_TABLE === 'BUSINESS_PARTNER') {
            switch (newType) {
              case 'BUSINESS_PARTNER-INST':
                url = `${app?.approvalPath}/institution-details/${ID}`;
                break;
              case 'BUSINESS_PARTNER-SUPP':
                url = `${app?.approvalPath}/suppliers-details/${ID}`;
                break;
              case 'BUSINESS_PARTNER-CUST':
                url = `${app?.approvalPath}/customer-details/${ID}`;
                break;
            }
          } else {
            url = `${app?.approvalPath}/details/${ID}`;
          }
        }
        return (
          <a className="rk-a-span" onClick={() => history.push(url)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'CREATED_TIME',
      width: 120,
      render: (dom, record) => {
        const { CREATED_TIME } = record;
        if (!CREATED_TIME) return null;
        return dayjs(CREATED_TIME).format('YYYY-MM-DD');
      },
      hideInSearch: true,
    },
    {
      title: '来源',
      dataIndex: 'SOURCE_TABLE',
      width: 120,
      hideInSearch: true,
      render: (dom, entity) => {
        const { newType } = entity;
        const types = option2enum(submitType);
        const val = types[newType!];
        return val ? <Tag>{val.text}</Tag> : dom;
      },
    },
    {
      title: '来源',
      dataIndex: 'SOURCE_TABLE',
      valueType: 'select',
      fieldProps: () => ({
        showSearch: true,
      }),
      valueEnum: option2enum(options),
      hideInTable: true,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      hideInTable: true,
      search: false,
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<Record<string, any>>
        {...defaultTableConfig}
        rowKey="ID"
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        columns={columns}
        params={{
          activiStatus: activeKey,
        }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          extraUrlParams: {
            activiStatus: activeKey,
          },
        }}
        toolbar={{
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: menuStatus,
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
        }}
        request={async (params) => {
          const { SOURCE_TABLE, DOCUMENT_NUMBER, pageSize, current, activiStatus } = params;
          const msg = await treatSubmit({
            searchNumber: DOCUMENT_NUMBER,
            searchType: SOURCE_TABLE,
            pageNum: current,
            pageSize: pageSize,
            activiStatus: activiStatus,
          });
          const dataSource = msg?.data?.treatSubmitRespList?.map((item) => {
            const { SOURCE_TABLE, DOCUMENT_NUMBER } = item;
            let newType;
            //类型为合作伙伴有客户、供应商、招标机构
            if (SOURCE_TABLE === 'BUSINESS_PARTNER') {
              if (DOCUMENT_NUMBER?.includes('CUST') === true) {
                newType = `${SOURCE_TABLE}-CUST`;
              } else if (DOCUMENT_NUMBER?.includes('INST') === true) {
                newType = `${SOURCE_TABLE}-INST`;
              } else if (DOCUMENT_NUMBER?.includes('SUPP') === true) {
                newType = `${SOURCE_TABLE}-SUPP`;
              }
            }
            return { ...item, newType: newType ?? item.SOURCE_TABLE };
          });

          return {
            data: dataSource || [],
            success: true,
            total: msg?.data?.count,
          };
        }}
      />
    </PageContainer>
  );
};

export default PendingSubmit;
