import ChangeLog from '@/components/ChangeLog';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { usePartnerList } from '@/hooks/usePartnerList';
import { useProjectList } from '@/hooks/useProjectList';
import { useUserList } from '@/hooks/useUserList';
import { createInnerContract, getInnerConById, updateInnerContract } from '@/services/oa/contract';
import { onSuccessAndGoBack, onSuccessAndRefresh, queryFormData } from '@/utils';
import { useLocation, useModel } from '@@/exports';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { useCounter } from 'ahooks';
import { Collapse } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ContractContentTable from '../../Main/components/ContractContentTable';
import PayInfo from '../../Purchase/components/PayInfo';
import PayPlan from '../../Purchase/components/PayPlan';
import BaseInfo from './BaseInfo';

// 创建一个上下文
const InternalDetails: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const { userList, loading: userLoading } = useUserList();
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const formRef = useRef<ProFormInstance>();
  const [current, { inc }] = useCounter(0);
  const { canEditInternalContract, canAddInternalContract, canSuperInternalContract } = useAccess();
  const canEdit =
    (canEditInternalContract && isEditPage) ||
    (!isEditPage && canAddInternalContract) ||
    canSuperInternalContract;
  const [detailsInfo, setDetailsInfo] = useState<API.InnerContractResp>();
  const { approvalDetails } = useModel('useApprovalModel');
  const codeRef = useRef(0);

  const { pathname } = useLocation();
  // 判断是否为审批页面
  const isApprovalPage = pathname.includes('/approval/');

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  // 获取项目
  const { projectList, loading: projectLoading } = useProjectList();

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createInnerContract(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updateInnerContract(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      codeRef.current = res.code;
      onSuccessAndRefresh(res, inc);
    },
    formatResult: (res) => res,
  });

  useEffect(() => {
    if (isApprovalPage && formRef.current && formRef.current?.setFieldsValue) {
      const res = JSON.parse(JSON.stringify(approvalDetails?.fromData));
      if (res?.innerConInfo?.spName) {
        res.payInfo = res.payInfo || {};
        res.payInfo.institutionName = res.innerConInfo.spName;
      }
      formRef.current.setFieldsValue(res);
    }
  }, [isApprovalPage, approvalDetails]);
  // 是否提交审批
  const isSubmit = !['1', '3', '4'].includes(detailsInfo?.innerConInfo?.activiStatus || '');

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.InnerContractResp>
        formRef={formRef}
        initialValues={{
          innerConInfo: {
            contractStatus: 'EXECUTING',
            firstParty: 'CLIENT',
            secondParty: 'CLIENT',
          },
        }}
        disabled={
          !isSubmit ||
          isApprovalPage ||
          !canEdit ||
          (isEditPage &&
            ['END', 'STOP'].includes(detailsInfo?.innerConInfo?.contractStatus || '') &&
            !canSuperInternalContract)
        }
        submitter={
          isSubmit &&
          !isApprovalPage &&
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
            },
          }
        }
        onFinish={async (values) => {
          if (isEditPage) {
            update(values);
          } else {
            add(values);
          }
        }}
        params={{ current }}
        request={async () => {
          const res = await queryFormData(
            {
              idReq: { id },
            },
            isEditPage && !isApprovalPage,
            getInnerConById,
          );
          if (res?.innerConInfo?.spName) {
            res.payInfo = res.payInfo || {};
            res.payInfo.institutionName = res.innerConInfo.spName;
          }
          setDetailsInfo(res);
          return res;
        }}
        onValuesChange={(val: API.InnerContractResp) => {
          if (val.innerConInfo?.projectNumber) {
            const project = projectList?.find(
              (item) => item.projectNumber === val?.innerConInfo?.projectNumber,
            );
            formRef.current?.setFieldValue(['innerConInfo', 'projectId'], project?.id);
            formRef.current?.setFieldValue(
              ['innerConInfo', 'proMangerId'],
              project?.projectMangerId,
            );
            formRef.current?.setFieldValue(['innerConInfo', 'proManger'], project?.projectManger);
          }

          if (val.innerConInfo?.firstParty) {
            formRef.current?.setFieldValue(['innerConInfo', 'fpId'], null);
          }

          if (val.innerConInfo?.secondParty) {
            formRef.current?.setFieldValue(['innerConInfo', 'spId'], null);
          }

          // 从乙方带出付款基本信息的单位名称
          if (val?.innerConInfo?.spId) {
            const partner = partnerList.find((item) => item.id === val?.innerConInfo?.spId);
            if (partner) {
              formRef.current?.setFieldValue(['payInfo', 'institutionName'], partner?.clientName);
            }
          }

          // 从乙方带出付款基本信息的单位名称-如果是输入框的情况
          if (val?.innerConInfo?.spName) {
            formRef.current?.setFieldValue(['payInfo', 'institutionName'], val.innerConInfo.spName);
          }
        }}
      >
        {isEditPage && (
          <ProFormDependency name={['innerConInfo']}>
            {({ innerConInfo }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : innerConInfo?.activiStatus
                  }
                  title={innerConInfo?.contractNumber}
                  approveType="INTERNAL_CONTRACT_APPROVAL"
                  onOperationCallback={async () => {
                    const res = await queryFormData(
                      {
                        idReq: { id },
                      },
                      isEditPage && !isApprovalPage,
                      getInnerConById,
                    );
                    setDetailsInfo(res);
                    formRef.current?.setFieldsValue(res);
                  }}
                  onSave={onSave}
                  saveDisabled={!canEditInternalContract}
                />
              );
            }}
          </ProFormDependency>
        )}
        <BaseListContext.Provider
          value={{
            userList,
            userLoading,
            projectList,
            projectLoading,
            partnerList,
            partnerLoading,
          }}
        >
          <Collapse defaultActiveKey={['1', '2', '3', '4']} ghost>
            <Collapse.Panel key="1" header="内部合同明细" collapsible="header">
              <BaseInfo />
            </Collapse.Panel>
            <Collapse.Panel key="2" header="合同执行情况描述" collapsible="header">
              <ContractContentTable />
            </Collapse.Panel>
            <Collapse.Panel key="3" header="付款基本信息" collapsible="header">
              <PayInfo />
            </Collapse.Panel>
            <Collapse.Panel key="4" header="付款计划" collapsible="header">
              <PayPlan
                disabled={
                  !isSubmit ||
                  isApprovalPage ||
                  !canEdit ||
                  (isEditPage &&
                    ['END', 'STOP'].includes(detailsInfo?.innerConInfo?.contractStatus || ''))
                }
                ticketDisabled={detailsInfo?.innerConInfo?.activiStatus !== '2'}
              />
            </Collapse.Panel>
          </Collapse>
        </BaseListContext.Provider>
        <ChangeLog />
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(InternalDetails);
