import DownloadModalForm from '@/components/ExportExcel/DownloadModalForm';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import {
  APPROVAL_STATUS,
  CONTRACT_CATEGORY,
  CONTRACT_CHARACTER,
  CONTRACT_SERVICES_CATEGORY,
  CONTRACT_STATUS,
  EXPIRED_STATE,
  SIGN_STATUS,
} from '@/enums';
import { rangePresets } from '@/pages/ReportAnalysis/components/DimensionSetting';
import { deleteMainConById, pageMainContract } from '@/services/oa/contract';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, Link as UmiLink, useAccess, useRequest } from '@umijs/max';
import { Button, message, Modal, Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

const { Link } = Typography;
const ContractMain: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.MainContractInfoResp[]>([]);
  const {
    canAddMasterContract = false,
    canExportMasterContract = false,
    canEditMasterContract,
    canDeleteMasterContract = false,
    canReadPreSalesCostConfig = false,
  } = useAccess();
  const [modalVisit, setModalVisit] = useState(false);

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteMainConById({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.MainContractInfoResp[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.contractName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除主合同“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.MainContractInfoResp>[] = [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 150,
      copyable: true,
      fixed: 'left',
      render(dom, entity) {
        return (
          <a
            className="rk-a-span"
            onClick={() => {
              if (canEditMasterContract) {
                history.push(`/contract/main/edit/${entity.id}`);
              } else {
                history.push(`/contract/main/details/${entity.id}`);
              }
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '服务类别',
      dataIndex: 'serveCategory',
      valueEnum: option2enum(CONTRACT_SERVICES_CATEGORY),
      width: 100,
    },
    {
      title: '审批状态',
      dataIndex: 'activiStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 100,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '内容概述',
      dataIndex: 'overview',
      width: 200,
      ellipsis: true,
    },
    {
      title: '性质',
      dataIndex: 'contractQuality',
      hideInSearch: true,
      width: 80,
      render(dom, entity) {
        const { contractQuality } = entity;
        const character = option2enum(CONTRACT_CHARACTER);
        const val = character[contractQuality!];
        return val ? <Tag color={val?.textColor}>{val.text}</Tag> : dom;
      },
    },
    {
      title: '甲方',
      dataIndex: 'fpName',
      ellipsis: true,
      width: 180,
    },
    {
      title: '销售姓名',
      dataIndex: 'salePerson',
      width: 120,
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
      valueType: 'select',
      width: 80,
      valueEnum: option2enum(CONTRACT_STATUS),
    },
    {
      title: '签订状态',
      dataIndex: 'signStatus',
      valueType: 'select',
      width: 100,
      valueEnum: option2enum(SIGN_STATUS),
    },
    {
      title: '状态',
      dataIndex: 'endTime',
      width: 80,
      renderText(text) {
        if (!text) return '1';
        const expired = dayjs(text).endOf('day').isBefore();
        return expired ? '1' : '0';
      },
      valueEnum: option2enum(EXPIRED_STATE),
    },
    {
      title: '合同类别',
      dataIndex: 'contractCategory',
      valueType: 'select',
      hideInTable: true,
      valueEnum: option2enum(CONTRACT_CATEGORY),
    },
    {
      title: '签订日期',
      valueType: 'dateRange',
      dataIndex: 'days',
      fieldProps: {
        presets: rangePresets,
      },
      hideInTable: true,
    },
    {
      title: '关联项目',
      width: 300,
      hideInSearch: true,
      render(_, entity) {
        const { proInfoList } = entity;
        if (!proInfoList?.length) return '-';
        return (
          <Space wrap>
            {proInfoList.map((item) => (
              <Link
                className="ant-btn-link"
                copyable
                key={item.projectId}
                onClick={() => {
                  history.push(`/project/sales/edit/${item.projectId}`);
                }}
              >
                {item.projectNumber}
              </Link>
            ))}
          </Space>
        );
      },
    },
    {
      title: '操作',
      fixed: 'right',
      width: 160,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !canDeleteMasterContract,
      render: (text, record) => {
        return (
          <Space>
            <Access accessible={canReadPreSalesCostConfig && record.activiStatus !== '1'}>
              <UmiLink to={`/contract/main/config/${record.id}`}>售前成本配置</UmiLink>
            </Access>
            <Access accessible={canDeleteMasterContract && record.activiStatus !== '1'}>
              <a onClick={() => handleDelete([record])}>删除</a>
            </Access>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.MainContractInfoResp>
        {...defaultTableConfig}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        rowSelection={
          canDeleteMasterContract
            ? {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                getCheckboxProps: (record) => ({
                  disabled: record?.activiStatus !== '0',
                }),
              }
            : false
        }
        columns={columns}
        headerTitle="主合同列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddMasterContract}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/contract/main/add');
                }}
              >
                新建主合同
              </Button>
            </Access>,
            <Access key="export" accessible={canExportMasterContract}>
              <Button
                key="download"
                type="primary"
                onClick={() => {
                  setModalVisit(true);
                }}
                icon={<DownloadOutlined />}
              >
                导出
              </Button>
            </Access>,
          ],
        }}
        request={async ({ endTime, activiStatus, serveCategory, days, ...params }) => {
          const filter = { activiStatus, serveCategory };
          const signDateScope =
            (days &&
              [
                {
                  key: 'ge',
                  name: 'signDate',
                  val: dayjs(days[0]).format('YYYY-MM-DD'),
                },
                {
                  key: 'le',
                  name: 'signDate',
                  val: dayjs(days[1]).format('YYYY-MM-DD'),
                },
              ].filter(Boolean)) ||
            [];
          const endTimeScope =
            (endTime &&
              [
                {
                  name: `TO_DATE(END_TIME, 'YYYY-MM-DD HH24:MI:SS')`,
                  key: endTime === '0' ? 'ge' : 'lt',
                  val: dayjs().format('YYYY-MM-DD'),
                },
              ].filter(Boolean)) ||
            [];
          return queryPagingTable(
            {
              scope: signDateScope.concat(endTimeScope),
              ...params,
              filter,
            },
            pageMainContract,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />

      {/* {导出} */}
      <DownloadModalForm
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        type="contract-main"
      />
    </PageContainer>
  );
};

export default ContractMain;
