import ChangeLog from '@/components/ChangeLog';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useAvailableMainContractList } from '@/hooks/useAvailableMainContractList';
import { usePartnerList } from '@/hooks/usePartnerList';
import { useUserList } from '@/hooks/useUserList';
import Receipt from '@/pages/Contract/Purchase/components/Receipt';
import { createPurContract, getPurConById, updatePurContract } from '@/services/oa/contract';
import { calcTaxRate, onSuccessAndGoBack, onSuccessAndRefresh, queryFormData } from '@/utils';
import { useLocation, useModel } from '@@/exports';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useAccess, useRequest } from '@umijs/max';
import { useCounter } from 'ahooks';
import { Collapse } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ContractContentTable from '../../Main/components/ContractContentTable';
import BaseInfo from './BaseInfo';
import DifferenceInfo from './DifferenceInfo';
import PayInfo from './PayInfo';
import PayPlan from './PayPlan';

// 创建一个上下文
const PurchaseDetails: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const { userList, loading: userLoading } = useUserList();
  const { contractList, loading: contractLoading } = useAvailableMainContractList();
  const { partnerList, loading: partnerLoading } = usePartnerList();
  const formRef = useRef<ProFormInstance>();
  const [current, { inc }] = useCounter(0);

  const [detailsInfo, setDetailsInfo] = useState<API.PurContractResp>();
  const { approvalDetails } = useModel('useApprovalModel');
  const codeRef = useRef(0);

  const { canEditPurchaseContract, canAddPurchaseContract, canSuperPurchaseContract } = useAccess();

  const { pathname } = useLocation();
  // 判断是否为审批页面
  const isApprovalPage = pathname.includes('/approval/');

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  const canEdit =
    (canEditPurchaseContract && isEditPage) ||
    (!isEditPage && canAddPurchaseContract) ||
    canSuperPurchaseContract;
  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createPurContract(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest((value) => updatePurContract(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      codeRef.current = res.code;
      onSuccessAndRefresh(res, inc);
    },
    formatResult: (res) => res,
  });

  useEffect(() => {
    if (isApprovalPage && formRef.current && formRef.current?.setFieldsValue)
      formRef.current.setFieldsValue(approvalDetails?.fromData);
  }, [isApprovalPage, approvalDetails]);
  // 是否提交审批
  const isSubmit = !['1', '3', '4'].includes(detailsInfo?.purContractInfo?.activiStatus || '');

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<API.PurContractResp>
        disabled={
          !isSubmit ||
          isApprovalPage ||
          !canEdit ||
          (isEditPage &&
            ['END', 'STOP'].includes(detailsInfo?.purContractInfo?.contractStatus || '') &&
            !canSuperPurchaseContract)
        }
        formRef={formRef}
        initialValues={{
          purContractInfo: {
            contractStatus: 'EXECUTING',
            firstParty: 'CLIENT',
            secondParty: 'CLIENT',
            contractAmount: 0,
            contractTax: 0,
          },
        }}
        submitter={
          isSubmit &&
          !isApprovalPage &&
          canEdit && {
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || editLoading,
            },
          }
        }
        onFinish={async (values) => {
          const form = JSON.parse(JSON.stringify(values));
          form?.payPlanDetailInfoList?.forEach((row: API.PayPlanDetailInfoReq) => {
            const { estimatePayAmount = 0, rate = 0 } = row || {};
            const { taxExclusiveAmount, taxInclusiveAmount } = calcTaxRate(estimatePayAmount, rate);
            row.excludeRaAmount = Number(taxExclusiveAmount);
            row.rateAmount = Number(taxInclusiveAmount);
          });

          if (isEditPage) {
            update(form);
          } else {
            add(form);
          }
        }}
        params={{ current }}
        request={async () => {
          const res = await queryFormData(
            {
              idReq: { id },
            },
            isEditPage && !isApprovalPage,
            getPurConById,
          );
          setDetailsInfo(res);

          return res;
        }}
        onValuesChange={(val: API.PurContractResp, values: API.PurContractResp) => {
          if (val.purContractInfo?.firstParty) {
            formRef.current?.setFieldValue(['purContractInfo', 'fpId'], null);
            formRef.current?.setFieldValue(['purContractInfo', 'endUserId'], null);
          }
          if (val.purContractInfo?.secondParty) {
            formRef.current?.setFieldValue(['purContractInfo', 'spId'], null);
          }
          // 关联合同
          if (val.purContractInfo?.mainConNumber) {
            const contract: API.MainContractInfoReq =
              contractList.find(
                (item) => item.contractNumber === val.purContractInfo?.mainConNumber,
              ) || {};
            formRef.current?.setFieldValue(['purContractInfo', 'mainConId'], contract?.id);
            formRef.current?.setFieldValue(
              ['purContractInfo', 'mainConName'],
              contract?.contractName,
            );
            formRef.current?.setFieldValue(
              ['purContractInfo', 'salePersonId'],
              contract?.salePersonId,
            );
          }

          // 框架协议，根据计划自动计算合同金额
          if (val.payPlanDetailInfoList && values?.purContractInfo?.isFrameworkAgreement) {
            const total = val.payPlanDetailInfoList.reduce(
              (total: number, item: API.PayPlanDetailInfoResp) =>
                total + (item.estimatePayAmount || 0),
              0,
            );
            formRef.current?.setFieldValue(['purContractInfo', 'contractAmount'], total);
          }

          // 框架协议，根据计划自动计算总税额
          if (val.payPlanDetailInfoList && values?.purContractInfo?.isFrameworkAgreement) {
            const total = val.payPlanDetailInfoList.reduce(
              (total: number, item: API.PayPlanDetailInfoResp) =>
                total + (Number(item.rateAmount) || 0),
              0,
            );
            formRef.current?.setFieldValue(['purContractInfo', 'contractTax'], total);
          }

          // 从乙方带出付款基本信息的单位名称
          if (val?.purContractInfo?.spId) {
            const partner = partnerList.find((item) => item.id === val?.purContractInfo?.spId);
            if (partner) {
              formRef.current?.setFieldValue(['payInfo', 'institutionName'], partner?.clientName);
            }
          }
        }}
      >
        {isEditPage && (
          <ProFormDependency name={['purContractInfo']}>
            {({ purContractInfo }) => {
              return (
                <RKPageHeader
                  id={id}
                  status={
                    isApprovalPage
                      ? (approvalDetails?.activiStatus as unknown as string) || '9'
                      : purContractInfo?.activiStatus
                  }
                  title={purContractInfo?.contractNumber}
                  approveType="PURCHASE_CONTRACT_APPROVAL"
                  onOperationCallback={async () => {
                    const res = await queryFormData(
                      {
                        idReq: { id },
                      },
                      isEditPage && !isApprovalPage,
                      getPurConById,
                    );
                    setDetailsInfo(res);

                    formRef.current?.setFieldsValue(res);
                  }}
                  onSave={onSave}
                  saveDisabled={!canEditPurchaseContract}
                />
              );
            }}
          </ProFormDependency>
        )}
        <BaseListContext.Provider
          value={{
            userList,
            userLoading,
            contractList,
            contractLoading,
            partnerList,
            partnerLoading,
          }}
        >
          <Collapse defaultActiveKey={['1', '2', '3', '4', '5', '6']} ghost>
            <Collapse.Panel key="1" header="采购合同明细" collapsible="header">
              <BaseInfo detailsInfo={detailsInfo} isApprovalPage={isApprovalPage} />
            </Collapse.Panel>
            {!pathname.includes('/add') && (
              <Collapse.Panel key="2" header="差额信息" collapsible="header">
                <DifferenceInfo contractId={id} />
              </Collapse.Panel>
            )}
            <Collapse.Panel key="3" header="合同执行情况描述" collapsible="header">
              <ContractContentTable />
            </Collapse.Panel>
            <Collapse.Panel key="4" header="付款基本信息" collapsible="header">
              <PayInfo />
            </Collapse.Panel>
            <Collapse.Panel key="5" header="付款计划" collapsible="header">
              <PayPlan
                disabled={
                  (isEditPage &&
                    ['END', 'STOP'].includes(detailsInfo?.purContractInfo?.contractStatus || '')) ||
                  !canEdit
                }
                ticketDisabled={detailsInfo?.purContractInfo?.activiStatus !== '2'}
              />
            </Collapse.Panel>
            <Collapse.Panel key="6" header="收票记录表" collapsible="header">
              <ProForm.Item name="collectTicketContractList">
                <Receipt />
              </ProForm.Item>
            </Collapse.Panel>
          </Collapse>
        </BaseListContext.Provider>
        <ChangeLog />
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(PurchaseDetails);
