import RKCol from '@/components/RKCol';
import RKSelectLabel from '@/components/RKSelectLabel';
import TitleLink from '@/components/TitleLink';
import BaseListContext from '@/Context/BaseListContext';
import { CONTRACT_SERVICES_CATEGORY_P, CONTRACT_STATUS, INDUSTRY, SIGN_STATUS } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormMoney,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { memo, useContext } from 'react';

const BaseInfo: React.FC<
  WithRouteEditingProps & { detailsInfo: Record<string, any> | undefined; isApprovalPage: boolean }
> = ({ isEditPage, detailsInfo, isApprovalPage }) => {
  const {
    userList,
    userLoading,
    contractList,
    contractLoading,
    partnerList = [],
    partnerLoading,
  } = useContext(BaseListContext);
  // 获取当前用户
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  return (
    <>
      <div className="rk-none">
        <ProFormText name={['purContractInfo', 'id']} />
        <ProFormText name={['purContractInfo', 'mainConId']} />
      </div>
      <Row gutter={24}>
        {isEditPage && (
          <RKCol>
            <ProFormText
              name={['purContractInfo', 'contractNumber']}
              label="合同编号"
              disabled={isEditPage}
            />
          </RKCol>
        )}
        <RKCol>
          <ProFormTextArea
            name={['purContractInfo', 'contractName']}
            label="合同名称"
            rules={[requiredRule]}
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['purContractInfo', 'serveCategory']}
            label="服务类别"
            rules={[requiredRule]}
            options={CONTRACT_SERVICES_CATEGORY_P}
          />
        </RKCol>
        <ProFormDependency name={[['purContractInfo', 'fpId']]}>
          {({ purContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={purContractInfo?.fpId && `/crm/customer/edit/${purContractInfo?.fpId}`}
                    >
                      甲方
                    </TitleLink>
                  }
                  name={['purContractInfo', 'fpId']}
                  rules={[requiredRule]}
                  fieldProps={{
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  params={partnerList}
                  request={async () => partnerList.filter((item) => item?.partnerType === 'CLIENT')}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={[['purContractInfo', 'spId']]}>
          {({ purContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={purContractInfo?.spId && `/crm/suppliers/edit/${purContractInfo?.spId}`}
                    >
                      乙方
                    </TitleLink>
                  }
                  name={['purContractInfo', 'spId']}
                  rules={[requiredRule]}
                  fieldProps={{
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    showSearch: true,
                    loading: partnerLoading,
                  }}
                  params={partnerList}
                  // 乙方来源供应商
                  request={async () => partnerList.filter((item) => item?.partnerType === 'VENDOR')}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={[['purContractInfo', 'endUserId']]}>
          {({ purContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  label={
                    <TitleLink
                      path={
                        purContractInfo?.endUserId &&
                        `/crm/customer/edit/${purContractInfo?.endUserId}`
                      }
                    >
                      最终用户
                    </TitleLink>
                  }
                  name={['purContractInfo', 'endUserId']}
                  dependencies={['purContractInfo', 'firstParty']}
                  rules={[requiredRule]}
                  fieldProps={{
                    fieldNames: {
                      label: 'clientName',
                      value: 'id',
                    },
                    loading: partnerLoading,
                    showSearch: true,
                  }}
                  params={partnerList}
                  request={async () => partnerList.filter((item) => item?.partnerType === 'CLIENT')}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormSelect
            name={['purContractInfo', 'industry']}
            label="行业"
            rules={[requiredRule]}
            options={INDUSTRY}
          />
        </RKCol>
        <ProFormDependency name={[['purContractInfo', 'mainConId']]}>
          {({ purContractInfo }) => {
            return (
              <RKCol>
                <ProFormSelect
                  name={['purContractInfo', 'mainConNumber']}
                  label={
                    <TitleLink
                      path={
                        purContractInfo?.mainConId &&
                        `/contract/main/edit/${purContractInfo?.mainConId}`
                      }
                    >
                      关联主合同编号
                    </TitleLink>
                  }
                  fieldProps={{
                    loading: contractLoading,
                    showSearch: true,
                    optionLabelProp: 'contractNumber',
                  }}
                  options={contractList?.map((item: Record<string, any>) => {
                    return {
                      value: item.contractNumber,
                      label: (
                        <RKSelectLabel
                          title={item.contractNumber}
                          info={item.contractName}
                          disabled={item.disabled}
                        />
                      ),
                      contractNumber: item.contractNumber,
                      disabled: item.disabled,
                    };
                  })}
                  rules={[requiredRule]}
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormText
            name={['purContractInfo', 'mainConName']}
            label="关联主合同名称"
            disabled={true}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['purContractInfo', 'salePersonId']}
            label="销售"
            rules={[requiredRule]}
            fieldProps={{
              loading: userLoading,
              fieldNames: {
                value: 'id',
                label: 'username',
              },
              showSearch: true,
            }}
            options={userList as DefaultOptionType[]}
          />
        </RKCol>
        <RKCol>
          <ProFormMoney
            name={['purContractInfo', 'contractAmount']}
            label="合同金额"
            locale="zh-CN"
            rules={[requiredRule]}
            min={0}
            disabled={isEditPage}
          />
        </RKCol>
        <ProFormDependency name={[['purContractInfo', 'isFrameworkAgreement']]}>
          {({ purContractInfo }) => {
            return (
              <RKCol>
                <ProFormMoney
                  name={['purContractInfo', 'contractTax']}
                  label="总税额"
                  locale="zh-CN"
                  rules={[requiredRule]}
                  min={0}
                  disabled={
                    (isEditPage && detailsInfo?.purContractInfo?.createdBy !== currentUser?.id) ||
                    !!purContractInfo?.isFrameworkAgreement ||
                    isApprovalPage
                  }
                />
              </RKCol>
            );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormSwitch
            name={['purContractInfo', 'isFrameworkAgreement']}
            label="框架协议"
            getValueFromEvent={(val) => (val ? 1 : 0)}
            getValueProps={(value) => ({ checked: value === 1 })}
          />
        </RKCol>

        <RKCol>
          <ProFormSelect
            name={['purContractInfo', 'contractStatus']}
            label="合同状态"
            rules={[requiredRule]}
            options={CONTRACT_STATUS}
          />
        </RKCol>
        <ProFormDependency name={[['purContractInfo', 'contractStatus']]}>
          {({ purContractInfo }) => {
            if (purContractInfo?.contractStatus === 'STOP')
              return (
                <RKCol>
                  <ProFormDatePicker
                    rules={[requiredRule]}
                    label="终止日期"
                    name={['purContractInfo', 'actuallyStopTime']}
                  />
                </RKCol>
              );
            if (purContractInfo?.contractStatus === 'END')
              return (
                <RKCol>
                  <ProFormDatePicker
                    rules={[requiredRule]}
                    label="结束日期"
                    name={['purContractInfo', 'actuallyEndTime']}
                  />
                </RKCol>
              );
          }}
        </ProFormDependency>
        <RKCol>
          <ProFormTextArea
            name={['purContractInfo', 'contractAddress']}
            label="合同所在地"
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 3,
              },
            }}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="开始日期"
            name={['purContractInfo', 'startTime']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="计划结束日期"
            name={['purContractInfo', 'endTime']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormDatePicker
            label="签订日期"
            name={['purContractInfo', 'signDate']}
            rules={[requiredRule]}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect
            name={['purContractInfo', 'signStatus']}
            label="签订状态"
            options={SIGN_STATUS}
          />
        </RKCol>
        <RKCol lg={24} md={24} sm={24}>
          <ProFormTextArea
            name={['purContractInfo', 'overview']}
            label="内容概述"
            fieldProps={{
              autoSize: {
                minRows: 2,
                maxRows: 4,
              },
            }}
          />
        </RKCol>
      </Row>
    </>
  );
};
export default memo(withRouteEditing(BaseInfo));
