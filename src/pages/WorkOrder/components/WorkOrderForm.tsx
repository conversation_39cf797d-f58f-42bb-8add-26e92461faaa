import BasicUpload, { UploaderRef } from '@/components/BasicUpload';
import RK<PERSON>ageHeader from '@/components/RKPageHeader';
import RKPageLoading from '@/components/RKPageLoading';
import BaseListContext from '@/Context/BaseListContext';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import { useAvailableProjectList } from '@/hooks/useAvailableProjectList';
import {
  createWorkOrder,
  getHistoricalContact,
  updateWorkOrder,
  workOrderInfo,
} from '@/services/oa/workOrder';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { useCounter } from 'ahooks';
import { Collapse, message } from 'antd';
import { UploadFileStatus } from 'antd/lib/upload/interface';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef } from 'react';
import BasicInfo from './BasicInfo';
import HoursEvents from './HoursEvents';

//计算时长
export const calculateDuration = (startTime: string, endTime: string) => {
  let workHours = 0;
  workHours = Math.abs(dayjs(endTime).diff(startTime, 'day') + 1) * 8;
  return workHours;
};

const WorkOrderForm: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  //判断是否为编辑页面,工单状态为通过时整个表单不可编辑
  const formRef = useRef<ProFormInstance>();
  const dataRef = useRef<API.WorkOrderInfoResp>();
  const { data: historyContactList = [], refresh: historyRefresh } = useRequest(() =>
    getHistoricalContact(),
  );

  //可用项目列表
  const { availableProjectList, loading: availableProjectLoading } = useAvailableProjectList();
  // 获取用户基本信息
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const [current] = useCounter(0);
  const codeRef = useRef(0);
  const uploadRef = useRef<UploaderRef>(null);

  useEffect(() => {
    uploadRef?.current?.fileList?.splice(0);
  }, [id]);

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createWorkOrder(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: edit, loading: editLoading } = useRequest((value) => updateWorkOrder(value), {
    manual: true,
    onSuccess: (res) => {
      codeRef.current = res.code;
      if (res.code !== 200) return;
      message.success('保存成功!');
      historyRefresh();
    },
    formatResult: (res) => res,
  });

  const { approvalDetails } = useModel('useApprovalModel');

  // 判断是否为审批页面
  const { pathname } = useLocation();
  const isApprovalPage = pathname.includes('/approval/');
  const { canEditWorkOrder, canAddWorkOrder } = useAccess();
  const isDisabled =
    (dataRef.current?.activiStatus && Number(dataRef.current?.activiStatus) > 0) ||
    isApprovalPage ||
    (isEditPage && !canEditWorkOrder) ||
    (!isEditPage && !canAddWorkOrder);

  useEffect(() => {
    if (isApprovalPage) {
      formRef.current?.setFieldsValue?.(approvalDetails?.fromData);
      approvalDetails?.fromData?.fileUrl?.forEach((item: string) => {
        const file = {
          uid: getRandomId(),
          status: 'done' as UploadFileStatus,
          name: '',
          url: item,
          thumbUrl: item,
        };
        uploadRef.current?.fileList?.push(file);
      });
    }
  }, [approvalDetails, isApprovalPage]);

  const onSave = useCallback(() => {
    return new Promise((resolve) => {
      codeRef.current = 0;
      formRef.current?.submit();

      const checkFormInterval = setInterval(() => {
        formRef.current
          ?.validateFields()
          .then(() => {
            const isFormSubmitted = codeRef.current === 200;
            if (isFormSubmitted) {
              clearInterval(checkFormInterval); // 清除轮询
              resolve(200); // 表单提交成功，resolve Promise
            }
          })
          .catch(() => {
            resolve(500);
            clearInterval(checkFormInterval); // 清除轮询
          });
      }, 1000); // 每秒检查一次表单是否提交成功
    });
  }, []);

  //获取详情
  const { loading, refresh } = useRequest(() => workOrderInfo({ req: { id } }), {
    ready: isEditPage && !isApprovalPage,
    onSuccess: (res) => {
      if (isApprovalPage) return {};
      const { fileUrl } = res as API.WorkOrderInfoResp;
      if (fileUrl?.length !== 0) {
        // 设置回显的fileList
        fileUrl?.forEach((item: string) => {
          const file = {
            uid: getRandomId(),
            name: '',
            url: item,
            thumbUrl: item,
            status: 'done' as UploadFileStatus,
          };
          uploadRef?.current?.fileList?.push(file!);
        });
      }
      dataRef.current = res;
      formRef?.current?.setFieldsValue({ ...res });
    },
  });

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <BaseListContext.Provider
        value={{
          availableProjectList,
          availableProjectLoading,
          historyContactList,
        }}
      >
        <RKPageLoading loading={loading} />
        <ProForm
          formRef={formRef}
          disabled={isDisabled}
          params={{ refresh: current }}
          submitter={
            isDisabled
              ? false
              : {
                  searchConfig: {
                    submitText: '保存',
                    resetText: '取消',
                  },
                  onReset: () => {
                    history.go(-1);
                  },

                  submitButtonProps: {
                    loading: addLoading || editLoading,
                  },
                  render: (props, doms) => {
                    return <FooterToolbar>{doms}</FooterToolbar>;
                  },
                }
          }
          onFinish={async (value) => {
            const { startTime, endTime } = value;
            if (new Date(endTime) > new Date(startTime)) {
              const formData = {
                ...value,
                fileUrl: uploadRef.current?.fileList.map((item) => item?.thumbUrl) ?? [],
              };
              const msg = isEditPage ? await edit(formData) : await add(formData);
              const success = msg.code === 200;
              return success;
            } else {
              message.error('结束时间必须大于开始时间');
            }
          }}
          onValuesChange={(val: API.WorkOrderUpdateReq, values) => {
            const { startTime, endTime } = values;
            //项目类型改变清空项目id和项目名称
            if (val.projectType) {
              formRef.current?.setFieldValue('projectId', undefined);
              formRef.current?.setFieldValue('projectNumber', undefined);
              formRef.current?.setFieldValue('projectName', undefined);
            }
            //根据项目编号回显项目名称
            if (val.projectNumber) {
              const projectObj = availableProjectList?.find(
                (item) => item.projectNumber === val.projectNumber,
              ) as API.ProBaseInfoResp;
              const { projectName, projectClassify, id } = projectObj;
              formRef.current?.setFieldValue('projectId', id);
              formRef.current?.setFieldValue('projectName', projectName);
              formRef.current?.setFieldValue('projectType', projectClassify);
            }
            if (val.startTime) {
              formRef.current?.setFieldValue(
                'workHours',
                calculateDuration(val.startTime, endTime),
              );
            }
            if (val.endTime) {
              formRef.current?.setFieldValue(
                'workHours',
                calculateDuration(startTime, val.endTime),
              );
            }
            if (val.contactName) {
              formRef.current?.setFieldValue(
                'contactPhone',
                historyContactList?.find((item) => item.contactName === val.contactName)
                  ?.contactPhone,
              );
            }
          }}
          initialValues={{
            employeeNumber: currentUser?.employeeNumber,
            employeeName: currentUser?.username,
            workHours: '0',
            projectType: 'SH',
          }}
        >
          {isEditPage && (
            <ProFormDependency name={['activiStatus', 'documentNumber']}>
              {({ activiStatus, documentNumber }) => {
                return (
                  <RKPageHeader
                    id={id}
                    status={
                      isApprovalPage
                        ? (approvalDetails?.activiStatus as unknown as string) || '9'
                        : activiStatus
                    }
                    title={documentNumber}
                    approveType="WORK_ORDER_APPROVAL"
                    onOperationCallback={() => {
                      uploadRef?.current?.fileList?.splice(0); //清空文件
                      // 如果提交审核成功，手动改状态，不调用刷新是为了防止页面闪动。
                      if (!isApprovalPage) {
                        refresh();
                      }
                    }}
                    print={
                      isApprovalPage
                        ? undefined
                        : {
                            printData: dataRef.current as Record<string, any>,
                            printType: 'csr',
                          }
                    }
                    onSave={onSave}
                    saveDisabled={!canEditWorkOrder}
                  />
                );
              }}
            </ProFormDependency>
          )}
          {/* 不需要展示，只是为了form传值 */}
          <div style={{ display: 'none' }}>
            <ProFormText name="id" label="id" placeholder="请输入" />
            <ProFormText name="projectNumber" />
            <ProFormText name="activiStatus" />
          </div>

          <Collapse defaultActiveKey={['1', '2']} ghost>
            <Collapse.Panel key="1" header="工单明细" collapsible="header">
              <BasicInfo />
            </Collapse.Panel>
            <Collapse.Panel key="2" header="工时和事件" collapsible="header">
              <HoursEvents />
              <ProForm.Item name="fileUrl" label="工单附件(目前仅支持图片或pdf格式的文件)">
                <BasicUpload ref={uploadRef} multiple={true} fileCount={5} />
              </ProForm.Item>
            </Collapse.Panel>
          </Collapse>
        </ProForm>
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default withRouteEditing(WorkOrderForm);
