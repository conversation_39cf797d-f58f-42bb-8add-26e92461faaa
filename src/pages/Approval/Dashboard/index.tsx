import { pendingApproval } from '@/services/oa/flow';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Badge, Col, Row, Space, Typography } from 'antd';
import React from 'react';

const colSpan = { xs: 12, sm: 12, md: 8, lg: 8, xl: 8 };
const { Text, Title } = Typography;
export const approvalList = [
  {
    title: '休假审批',
    path: '/approval/leave',
    key: 'LEAVE_APPROVAL_PROCESS',
  },
  {
    title: '销假审批',
    path: '/approval/revoke-leave',
    key: 'REVOCATION_APPROVAL',
  },
  {
    title: '报销审批',
    path: '/approval/expense-reimbursement',
    key: 'REIMBURSEMENT_APPROVAL',
  },
  {
    title: '培训报销审批',
    path: '/approval/training-reimbursement',
    key: 'TRAINING_REIMBURSEMENT_APPROVAL',
  },
  {
    title: '工单审批',
    path: '/approval/work-order',
    key: 'WORK_ORDER_APPROVAL',
  },
  {
    title: '内部项目审批',
    path: '/approval/internal-project',
    key: 'PROJECT_APPROVAL_NB',
  },
  {
    title: '销售项目审批',
    path: '/approval/sales-project',
    key: 'PROJECT_APPROVAL_XS',
  },
  {
    title: '售前项目审批',
    path: '/approval/pre-sales-project',
    key: 'PROJECT_APPROVAL_SQ',
  },
  {
    title: '售后项目审批',
    path: '/approval/after-sales-project',
    key: 'PROJECT_APPROVAL_SH',
  },
  {
    title: '开发项目审批',
    path: '/approval/develop-project',
    key: 'PROJECT_APPROVAL_KF',
  },
  {
    title: '业务伙伴审核',
    path: '/approval/partner',
    key: 'BUSINESS_PARTNER',
  },
  {
    title: '项目付款审批',
    path: '/approval/general-payment',
    key: 'UNIVERSAL_PAYMENT',
  },
  {
    title: '合同付款审批',
    path: '/approval/contract-payment',
    key: 'CONTRACT_PROCUREMENT_PAYMENT_APPLICATION',
  },
  {
    title: '资源使用审批',
    path: '/approval/resource-borrow',
    key: 'RESOURCE_APPLICATION',
  },
  {
    title: '招聘申请审批',
    path: '/approval/recruitment',
    key: 'RECRUITMENT_APPLICATION',
  },
  {
    title: '主合同审批',
    path: '/approval/main-contract',
    key: 'MAIN_CONTRACT_APPROVAL',
  },
  {
    title: '采购合同审批',
    path: '/approval/purchase-contract',
    key: 'PURCHASE_CONTRACT_APPROVAL',
  },
  {
    title: '内部合同审批',
    path: '/approval/internal-contract',
    key: 'INTERNAL_CONTRACT_APPROVAL',
  },
  {
    title: '公告审批',
    path: '/approval/announcement',
    key: 'ANNOUNCEMENT_APPROVAL',
  },
];
const Dashboard: React.FC = () => {
  const { data } = useRequest(() => pendingApproval(), {});
  return (
    <PageContainer header={{ title: false }}>
      <div>
        <ProCard gutter={24} title="审批管理" wrap>
          {approvalList.map((item, index) => (
            <ProCard
              style={{ marginBottom: 24 }}
              colSpan={colSpan}
              bordered
              hoverable
              key={index}
              onClick={() => {
                history.push(item.path);
              }}
            >
              <Badge dot count={data?.[item.key] || 0} offset={[8, 0]}>
                <Row gutter={16}>
                  <Col>
                    <img src="/images/approval.png" width={48} />
                  </Col>
                  <Col>
                    <Title level={5}>{item.title}</Title>
                    <Space>
                      <Text type="secondary">待处理:</Text>
                      <a>{data?.[item.key] || 0}</a>
                    </Space>
                  </Col>
                </Row>
              </Badge>
            </ProCard>
          ))}
        </ProCard>
      </div>
    </PageContainer>
  );
};

export default Dashboard;
