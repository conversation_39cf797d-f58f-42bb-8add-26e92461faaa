/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: '登录',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/personal-center',
    name: '个人中心',
    icon: 'home',
    access: 'canReadHome',
    routes: [
      {
        path: '/personal-center',
        redirect: '/personal-center/home',
      },
      {
        path: '/personal-center/home',
        component: './PersonalCenter',
      },
      {
        path: '/personal-center/pending-dispose',
        name: '待处理',
        component: './PersonalCenter/components/PendingDispose',
        hideInMenu: true,
      },
      {
        path: '/personal-center/pending-submit',
        name: '我的申请',
        component: './PersonalCenter/components/PendingSubmit',
        hideInMenu: true,
      },
      // {
      //   path: '/personal-center/pending-task',
      //   component: './PersonalCenter/components/Task/component/TaskTable',
      //   name: '我的任务',
      //   hideInMenu: true,
      // },
    ],
  },

  {
    path: '/human-resources',
    name: '人力资源',
    icon: 'team',
    access: 'canReadHR',
    routes: [
      {
        path: '/human-resources',
        redirect: '/human-resources/employees',
      },
      {
        path: '/human-resources/employees',
        name: '员工列表',
        component: './HumanResources/Employees',
        access: 'canReadEmployee',
      },
      {
        path: '/human-resources/employees/add',
        name: '新建',
        component: './HumanResources/Employees/components/EmployeeForm',
        hideInMenu: true,
        access: 'canAddEmployee',
      },
      {
        path: '/human-resources/employees/edit/:id',
        name: '编辑',
        component: './HumanResources/Employees/components/EmployeeForm',
        hideInMenu: true,
        access: 'canReadEmployee',
      },
      {
        path: '/human-resources/leave-application',
        name: '休假申请',
        component: './HumanResources/LeaveApplication',
        access: 'canReadLeaveApplication',
      },
      {
        path: '/human-resources/calendar',
        name: '休假日历',
        component: './HumanResources/LeaveApplication/components/Calendar',
        hideInMenu: true,
        access: 'canReadLeaveApplication',
      },
      {
        path: '/human-resources/leave-sell',
        name: '销假申请',
        component: './HumanResources/LeaveSell',
        access: 'canReadRevokeLeaveApplication',
      },
      {
        path: '/human-resources/attendance',
        name: '考勤',
        component: './HumanResources/Attendance',
        access: 'canReadAttendance',
      },
      {
        path: '/human-resources/attendance/attendance-detail/:id',
        name: '详情',
        component: './HumanResources/Attendance/components/AttendanceDetail',
        hideInMenu: true,
        access: 'canReadAttendance',
      },
      {
        path: '/human-resources/address-book',
        name: '通讯汇总',
        component: './HumanResources/AddressBook',
        access: 'canReadAddressBook',
      },
      {
        path: '/human-resources/billing-information',
        name: '开票信息',
        component: './HumanResources/BillingInformation',
        access: 'canReadInvoiceInformation',
      },
      {
        path: '/human-resources/system-management',
        name: '制度管理',
        component: './HumanResources/SystemManagement',
        access: 'canReadSystemManagement',
      },
      {
        path: '/human-resources/resource-application',
        name: '资源使用申请',
        component: './HumanResources/ResourceApplication',
        access: 'canReadResourceApplication',
      },
      {
        path: '/human-resources/recruitment-application',
        name: '招聘申请',
        component: './HumanResources/RecruitmentApplication',
        access: 'canReadRecruitmentApplication',
      },
      {
        path: '/human-resources/recruitment-application/add',
        name: '新建',
        component:
          './HumanResources/RecruitmentApplication/components/RecruitmentApplicationDetail',
        hideInMenu: true,
        access: 'canAddRecruitmentApplication',
      },
      {
        path: '/human-resources/recruitment-application/details/:id',
        name: '详情',
        component:
          './HumanResources/RecruitmentApplication/components/RecruitmentApplicationDetail',
        hideInMenu: true,
        access: 'canReadRecruitmentApplication',
      },
      {
        path: '/human-resources/talent-pool',
        name: '人才库',
        component: './HumanResources/TalentPool',
        access: 'canReadTalentPool',
      },
      {
        path: '/human-resources/talent-pool/add',
        name: '新建',
        component: './HumanResources/TalentPool/components/TalentPoolDetail',
        hideInMenu: true,
        access: 'canAddTalentPool',
      },
      {
        path: '/human-resources/talent-pool/details/:id',
        name: '详情',
        component: './HumanResources/TalentPool/components/TalentPoolDetail',
        hideInMenu: true,
        access: 'canReadTalentPool',
      },
      {
        path: '/human-resources/announcement',
        name: '公告管理',
        component: './HumanResources/Announcement',
      },
    ],
  },
  {
    path: '/crm',
    name: '业务伙伴',
    icon: 'shop',
    access: 'canReadCRM',
    routes: [
      {
        path: '/crm',
        redirect: '/crm/customer',
      },
      {
        path: '/crm/customer',
        name: '客户',
        component: './CRM/Customer',
        access: 'canReadCustomer',
      },
      {
        path: '/crm/customer/add',
        name: '新建客户',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
        access: 'canAddCustomer',
      },
      {
        path: '/crm/customer/edit/:id',
        name: '编辑客户',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
        access: 'canReadCustomer',
      },
      {
        path: '/crm/institution',
        name: '招标机构',
        component: './CRM/Institution',
        access: 'canReadTenderingAgency',
      },
      {
        path: '/crm/institution/add',
        name: '新建招标机构',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
        access: 'canAddTenderingAgency',
      },
      {
        path: '/crm/institution/edit/:id',
        name: '编辑招标机构',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
        access: 'canReadTenderingAgency',
      },
      {
        path: '/crm/suppliers',
        name: '供应商',
        component: './CRM/Suppliers',
        access: 'canReadSupplier',
      },
      {
        path: '/crm/suppliers/add',
        name: '新建供应商',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
        access: 'canAddSupplier',
      },
      {
        path: '/crm/suppliers/edit/:id',
        name: '编辑供应商',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
        access: 'canReadSupplier',
      },
      {
        path: '/crm/correlation',
        name: '单位名称与客户关联表',
        component: './CRM/Correlation',
        access: 'canReadCorrelation',
      },
    ],
  },
  {
    path: '/contract',
    name: '合同管理',
    icon: 'fileText',
    access: 'canReadContract',
    routes: [
      {
        path: '/contract',
        redirect: '/contract/main',
      },
      {
        path: '/contract/main',
        name: '主合同',
        component: './Contract/Main',
        access: 'canReadMasterContract',
      },
      {
        path: '/contract/main/add',
        name: '新建主合同',
        component: './Contract/Main/components/MainDetails',
        hideInMenu: true,
        access: 'canAddMasterContract',
      },
      {
        path: '/contract/main/edit/:id',
        name: '编辑主合同',
        component: './Contract/Main/components/MainDetails',
        hideInMenu: true,
        access: 'canReadMasterContract',
      },
      {
        path: '/contract/main/config/:id',
        name: '售前成本配置',
        component: './Contract/Main/components/PreSalesCostConfig',
        hideInMenu: true,
        access: 'canReadPreSalesCostConfig',
      },
      {
        path: '/contract/main/apply/:id',
        name: '开票申请',
        component: './Finance/Invoice/Invoicing/components/ApplyInvoice',
        hideInMenu: true,
        access: 'canReadMasterContract',
      },
      {
        path: '/contract/main/details/:id',
        name: '主合同详情',
        component: './Contract/Main/components/MainDetails',
        hideInMenu: true,
        access: 'canReadMasterContract',
      },
      {
        path: '/contract/purchase',
        name: '采购合同',
        component: './Contract/Purchase',
        access: 'canReadPurchaseContract',
      },

      {
        path: '/contract/purchase/add',
        name: '新建采购合同',
        component: './Contract/Purchase/components/PurchaseDetails',
        hideInMenu: true,
        access: 'canAddPurchaseContract',
      },
      {
        path: '/contract/purchase/edit/:id',
        name: '编辑采购合同',
        component: './Contract/Purchase/components/PurchaseDetails',
        hideInMenu: true,
        access: 'canReadPurchaseContract',
      },
      {
        path: '/contract/internal',
        name: '内部合同',
        component: './Contract/Internal',
        access: 'canReadInternalContract',
      },
      {
        path: '/contract/internal/add',
        name: '新建内部合同',
        component: './Contract/Internal/components/InternalDetails',
        hideInMenu: true,
        access: 'canAddInternalContract',
      },
      {
        path: '/contract/internal/edit/:id',
        name: '编辑内部合同',
        component: './Contract/Internal/components/InternalDetails',
        hideInMenu: true,
        access: 'canReadInternalContract',
      },
    ],
  },
  {
    path: '/finance',
    name: '财务管理',
    icon: 'FolderView',
    access: 'canReadFinance',
    routes: [
      {
        path: '/finance',
        redirect: '/finance/reimbursement',
      },
      {
        path: '/finance/reimbursement',
        name: '报销管理',
        access: 'canReadExpenseReimbursementManagement',
        routes: [
          {
            path: '/finance/reimbursement',
            redirect: '/finance/reimbursement/list',
          },
          {
            path: '/finance/reimbursement/list',
            name: '报销列表',
            component: './Finance/Reimbursement/ReimbursementList',
            access: 'canReadExpenseReimbursement',
          },
          {
            path: '/finance/reimbursement/list/add',
            name: '新建',
            component: './Finance/Reimbursement/ReimbursementList/components/ReimbursementListForm',
            hideInMenu: true,
            access: 'canAddExpenseReimbursement',
          },
          {
            path: '/finance/reimbursement/list/edit/:id',
            name: '编辑',
            component: './Finance/Reimbursement/ReimbursementList/components/ReimbursementListForm',
            hideInMenu: true,
            access: 'canReadExpenseReimbursement',
          },
          {
            path: '/finance/reimbursement/training',
            name: '培训报销',
            component: './Finance/Reimbursement/Training',
            access: 'canReadTrainingExpenseReimbursement',
          },
          // {
          //   path: '/finance/reimbursement/statistical',
          //   name: '报销统计',
          //   component: './Finance/Reimbursement/Statistical',
          //   access: 'canReadExpenseReimbursementStatistics',
          // },
        ],
      },
      {
        path: '/finance/payment',
        name: '支付申请',
        access: 'canReadPaymentRequest',
        routes: [
          {
            path: '/finance/payment',
            redirect: '/finance/payment/general',
          },
          {
            path: '/finance/payment/general',
            name: '项目付款申请',
            component: './Finance/Payment/General',
            access: 'canReadGeneral',
          },
          {
            path: '/finance/payment/general/add',
            name: '新建',
            component: './Finance/Payment/General/components/GeneralPaymentDetails',
            hideInMenu: true,
            access: 'canAddGeneral',
          },
          {
            path: '/finance/payment/general/details/:id',
            name: '详情',
            component: './Finance/Payment/General/components/GeneralPaymentDetails',
            hideInMenu: true,
            access: 'canReadGeneral',
          },
          {
            path: '/finance/payment/contract',
            name: '合同付款申请',
            component: './Finance/Payment/Contract',
            access: 'canReadContractPayment',
          },
          {
            path: '/finance/payment/contract/:id',
            name: '详情',
            component: './Finance/Payment/Contract/components/PaymentApplication',
            hideInMenu: true,
            access: 'canReadContractPayment',
          },
        ],
      },
      {
        path: '/finance/invoice',
        name: '发票管理',
        access: 'canReadInvoice',
        routes: [
          {
            path: '/finance/invoice',
            redirect: '/finance/invoice/invoicing-list',
          },
          {
            path: '/finance/invoice/invoicing-list',
            name: '开票记录',
            component: './Finance/Invoice/Invoicing',
            access: 'canReadInvoiceTransactionRecord',
          },
          {
            path: '/finance/invoice/invoicing-list/:id',
            name: '开票详情',
            component: './Finance/Invoice/Invoicing/components/ApplyInvoice',
            hideInMenu: true,
            access: 'canReadInvoiceTransactionRecord',
          },
          {
            path: '/finance/invoice/collect-list',
            name: '收票记录',
            component: './Finance/Invoice/Collect',
            access: 'canReadInvoiceCollectRecord',
          },
          {
            path: '/finance/invoice/collect-list/add',
            name: '新建收票记录',
            component: './Finance/Invoice/Collect/components/CollectDetails',
            hideInMenu: true,
            access: 'canAddInvoiceCollectRecord',
          },
          {
            path: '/finance/invoice/collect-list/edit/:id',
            name: '编辑收票记录',
            component: './Finance/Invoice/Collect/components/CollectDetails',
            hideInMenu: true,
            access: 'canReadInvoiceCollectRecord',
          },
          {
            path: '/finance/invoice/pending-collection',
            name: '合同待收票记录',
            component: './Finance/Invoice/PendingCollection',
            access: 'canReadInvoicePendingCollection',
          },
          {
            path: '/finance/invoice/pending-project',
            name: '项目待收票记录',
            component: './Finance/Invoice/PendingProject',
            access: 'canReadInvoicePendingCollection',
          },
        ],
      },
      {
        path: '/finance/JR',
        name: '收支管理',
        access: 'canReadTransaction',
        routes: [
          {
            path: '/finance/JR',
            redirect: '/finance/JR/receipt-list',
          },
          {
            path: '/finance/JR/receipt-list',
            name: '收款记录',
            component: './Finance/JR/Receipt',
            access: 'canReadReceiptTransactionRecord',
          },
          {
            path: '/finance/JR/receipt-list/add',
            name: '新建收款',
            component: './Finance/JR/Receipt/components/ReceiptDetails',
            hideInMenu: true,
            access: 'canAddReceiptTransactionRecord',
          },
          {
            path: '/finance/JR/receipt-list/details/:id',
            name: '收款详情',
            component: './Finance/JR/Receipt/components/ReceiptDetails',
            hideInMenu: true,
            access: 'canReadReceiptTransactionRecord',
          },
          {
            path: '/finance/JR/outstanding-payment',
            name: '待付款记录',
            component: './Finance/JR/OutstandingPayment',
            access: 'canReadOutstandingPaymentTransactionRecord',
          },
          {
            path: '/finance/JR/outstanding-payment/:id',
            name: '详情',
            component: './Finance/JR/Payment/components/PaymentRecord',
            hideInMenu: true,
            access: 'canReadOutstandingPaymentTransactionRecord',
          },
          {
            path: '/finance/JR/payment-list',
            name: '付款记录',
            component: './Finance/JR/Payment',
            access: 'canReadPaymentRecord',
          },
          {
            path: '/finance/JR/payment-list/:id',
            name: '详情',
            component: './Finance/JR/Payment/components/PaymentRecord',
            hideInMenu: true,
            access: 'canReadPaymentRecord',
          },
          {
            path: '/finance/JR/backs',
            name: '退款记录',
            component: './Finance/JR/Backs',
            access: 'canReadRefundHistory',
          },
          {
            path: '/finance/JR/backs/:id',
            name: '详情',
            component: './Finance/JR/Backs/components/BacksRecord',
            hideInMenu: true,
            access: 'canReadRefundHistory',
          },
          {
            path: '/finance/JR/refund',
            name: '销账管理',
            component: './Finance/JR/Refund',
            access: 'canAccountReceivable',
          },
        ],
      },
      {
        path: '/finance/payroll',
        name: '工资表',
        access: 'canReadPayroll',
        routes: [
          {
            path: '/finance/payroll',
            redirect: '/finance/payroll/salary',
          },
          {
            path: '/finance/payroll/salary',
            name: '工资基础信息',
            component: './Finance/Payroll',
            access: 'canReadSalaryRecord',
          },
        ],
      },
      {
        path: '/finance/invoice-bank',
        name: '开票银行',
        component: './Finance/InvoiceBank',
        access: 'canInvoiceBank',
      },
    ],
  },
  {
    path: '/project',
    name: '项目管理',
    icon: 'FolderOpen',
    access: 'canReadProject',
    routes: [
      {
        path: '/project',
        redirect: '/project/pre-sales',
      },
      {
        path: '/project/pre-sales',
        name: '售前项目',
        component: './Project/PreSales',
        access: 'canReadPreSalesProject',
      },
      {
        path: '/project/pre-sales/add',
        name: '新建售前项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canAddPreSalesProject',
      },
      {
        path: '/project/pre-sales/edit/:id',
        name: '编辑售前项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canReadPreSalesProject',
      },
      {
        path: '/project/after-sales',
        name: '售后项目',
        component: './Project/AfterSales',
        access: 'canReadAfterSalesProject',
      },
      {
        path: '/project/after-sales/add',
        name: '新建售后项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canAddAfterSalesProject',
      },
      {
        path: '/project/after-sales/edit/:id',
        name: '编辑售后项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canReadAfterSalesProject',
      },
      {
        path: '/project/sales',
        name: '销售项目',
        component: './Project/Sales',
        access: 'canReadSalesProject',
      },
      {
        path: '/project/sales/add',
        name: '新建销售项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canAddSalesProject',
      },
      {
        path: '/project/sales/edit/:id',
        name: '编辑销售项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canReadSalesProject',
      },
      {
        path: '/project/develop',
        name: '开发项目',
        component: './Project/Develop',
        access: 'canReadDevelopProject',
      },
      {
        path: '/project/develop/add',
        name: '新建开发项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canAddDevelopProject',
      },
      {
        path: '/project/develop/edit/:id',
        name: '编辑开发项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canEditDevelopProject',
      },
      {
        path: '/project/internal',
        name: '内部项目',
        component: './Project/Internal',
        access: 'canReadInternalProject',
      },
      {
        path: '/project/internal/add',
        name: '新建内部项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canAddInternalProject',
      },
      {
        path: '/project/internal/edit/:id',
        name: '编辑内部项目',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
        access: 'canReadInternalProject',
      },
    ],
  },
  {
    path: '/business',
    name: '商务管理',
    icon: 'MoneyCollect',
    access: 'canReadBusiness',
    routes: [
      {
        path: '/business',
        redirect: '/business/qualification',
      },
      {
        path: '/business/qualification',
        name: '公司资质',
        component: './Business/Qualification',
        access: 'canReadQualificationManagement',
      },
      {
        path: '/business/bid-account',
        name: '投标账号管理',
        component: './Business/BidAccount',
        access: 'canReadBiddingAccountManagement',
      },
      {
        path: '/business/certificate',
        name: '人员证书管理',
        component: './Business/Certificate',
        access: 'canReadCertificateManagement',
      },
    ],
  },
  {
    path: '/weekly-report',
    name: '周报',
    icon: 'schedule',
    access: 'canReadWR',
    routes: [
      {
        path: '/weekly-report',
        redirect: '/weekly-report/list',
      },
      {
        path: '/weekly-report/list',
        name: '周报列表',
        component: './WeeklyReport',
        hideInMenu: true,
        access: 'canReadWeeklyReport',
      },
      {
        path: '/weekly-report/list1',
        name: '周报列表1',
        component: './Demo',
      },
      {
        path: '/weekly-report/list/:id',
        name: '周报详情',
        component: './WeeklyReport/components/Report',
        hideInMenu: true,
        access: 'canReadWeeklyReport',
      },
    ],
  },
  {
    path: '/task',
    name: '任务管理',
    icon: 'orderedList',
    access: 'isRoleUser',
    routes: [
      {
        path: '/task',
        redirect: '/task/list',
      },
      {
        path: '/task/list',
        name: '任务列表',
        component: './Task',
        hideInMenu: true,
      },
      {
        path: '/task/add',
        name: '新建',
        component: './Task/components/TaskForm',
        hideInMenu: true,
      },
      {
        path: '/task/edit/:id',
        name: '编辑',
        component: './Task/components/TaskForm',
        hideInMenu: true,
      },
      {
        path: '/task/details/:id',
        name: '详情',
        component: './Task/components/TaskForm',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/work-orders',
    name: '工单管理',
    icon: 'solution',
    access: 'canReadWO',
    routes: [
      {
        path: '/work-orders',
        redirect: '/work-orders/list',
      },
      {
        path: '/work-orders/list',
        component: './WorkOrder',
        access: 'canReadWorkOrder',
      },
      {
        path: '/work-orders/add',
        name: '新建',
        component: './WorkOrder/components/WorkOrderForm',
        hideInMenu: true,
        access: 'canAddWorkOrder',
      },
      {
        path: '/work-orders/edit/:id',
        name: '编辑',
        component: './WorkOrder/components/WorkOrderForm',
        hideInMenu: true,
        access: 'canReadWorkOrder',
      },
    ],
  },
  {
    path: '/approval',
    name: '审批管理',
    icon: 'audit',
    access: 'canReadApproval',
    routes: [
      {
        path: '/approval',
        redirect: '/approval/home',
      },
      {
        path: '/approval/home',
        component: './Approval/Dashboard',
      },
      {
        path: '/approval/leave',
        name: '休假审批',
        component: './Approval/LeaveApproval/index',
        hideInMenu: true,
      },
      {
        path: '/approval/leave/details/:id',
        name: '详情',
        component: './Approval/LeaveApproval/Details',
        hideInMenu: true,
      },
      {
        path: '/approval/expense-reimbursement',
        name: '报销审批',
        component: './Approval/ExpenseReimbursement/index',
        hideInMenu: true,
      },
      {
        path: '/approval/expense-reimbursement/details/:id',
        name: '详情',
        component: './Finance/Reimbursement/ReimbursementList/components/ReimbursementListForm',
        hideInMenu: true,
      },
      {
        path: '/approval/training-reimbursement',
        name: '培训报销审批',
        component: './Approval/TrainingReimbursement/index',
        hideInMenu: true,
      },
      {
        path: '/approval/training-reimbursement/details/:id',
        name: '详情',
        component: './Approval/TrainingReimbursement/Details',
        hideInMenu: true,
      },
      {
        path: '/approval/partner',
        name: '业务伙伴审核',
        component: './Approval/Partner/index',
        hideInMenu: true,
      },
      {
        path: '/approval/partner/suppliers-details/:id',
        name: '详情',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/partner/institution-details/:id',
        name: '详情',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/partner/customer-details/:id',
        name: '详情',
        component: './CRM/Customer/components/CustomerDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/general-payment',
        name: '项目付款审批',
        component: './Approval/GeneralPayment/index',
        hideInMenu: true,
      },
      {
        path: '/approval/general-payment/details/:id',
        name: '详情',
        component: './Finance/Payment/General/components/GeneralPaymentDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/contract-payment',
        name: '合同付款审批',
        component: './Approval/ContractPayment/index',
        hideInMenu: true,
      },
      {
        path: '/approval/contract-payment/details/:id',
        name: '详情',
        component: './Finance/Payment/Contract/components/PaymentApplication',
        hideInMenu: true,
      },

      {
        path: '/approval/after-sales-project',
        name: '售后项目审批',
        component: './Approval/AfterSalesProject/index',
        hideInMenu: true,
      },
      {
        path: '/approval/after-sales-project/details/:id',
        name: '详情',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
      },
      {
        path: '/approval/pre-sales-project',
        name: '售前项目审批',
        component: './Approval/PreSalesProject/index',
        hideInMenu: true,
      },
      {
        path: '/approval/pre-sales-project/details/:id',
        name: '详情',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
      },
      {
        path: '/approval/sales-project',
        name: '销售项目审批',
        component: './Approval/SalesProject/index',
        hideInMenu: true,
      },
      {
        path: '/approval/sales-project/details/:id',
        name: '详情',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
      },
      {
        path: '/approval/internal-project',
        name: '内部项目审批',
        component: './Approval/InternalProject/index',
        hideInMenu: true,
      },
      {
        path: '/approval/internal-project/details/:id',
        name: '详情',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
      },
      {
        path: '/approval/develop-project',
        name: '开发项目审批',
        component: './Approval/DevelopProject/index',
        hideInMenu: true,
      },
      {
        path: '/approval/develop-project/details/:id',
        name: '详情',
        component: './Project/components/ProjectForm',
        hideInMenu: true,
      },
      {
        path: '/approval/work-order',
        name: '工单审批',
        component: './Approval/WorkOrder/index',
        hideInMenu: true,
      },
      {
        path: '/approval/work-order/details/:id',
        name: '详情',
        component: './WorkOrder/components/WorkOrderForm',
        hideInMenu: true,
      },
      {
        path: '/approval/revoke-leave',
        name: '销假审批',
        component: './Approval/RevokeLeave/index',
        hideInMenu: true,
      },
      {
        path: '/approval/revoke-leave/details/:id',
        name: '详情',
        component: './Approval/RevokeLeave/Details',
        hideInMenu: true,
      },
      {
        path: '/approval/resource-borrow',
        name: '资源使用审批',
        component: './Approval/ResourceBorrow/index',
        hideInMenu: true,
      },
      {
        path: '/approval/resource-borrow/details/:id',
        name: '详情',
        component: './Approval/ResourceBorrow/Details',
        hideInMenu: true,
      },
      {
        path: '/approval/recruitment',
        name: '招聘申请审批',
        component: './Approval/RecruitmentApproval/index',
        hideInMenu: true,
      },
      {
        path: '/approval/recruitment/details/:id',
        name: '详情',
        component: './Approval/RecruitmentApproval/Details',
        hideInMenu: true,
      },
      {
        path: '/approval/main-contract',
        name: '主合同审批',
        component: './Approval/MainContract/index',
        hideInMenu: true,
      },
      {
        path: '/approval/main-contract/details/:id',
        name: '详情',
        component: './Contract/Main/components/MainDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/purchase-contract',
        name: '采购合同审批',
        component: './Approval/PurchaseContract/index',
        hideInMenu: true,
      },
      {
        path: '/approval/purchase-contract/details/:id',
        name: '详情',
        component: './Contract/Purchase/components/PurchaseDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/internal-contract',
        name: '内部下单合同审批',
        component: './Approval/InternalContract/index',
        hideInMenu: true,
      },
      {
        path: '/approval/internal-contract/details/:id',
        name: '详情',
        component: './Contract/Internal/components/InternalDetails',
        hideInMenu: true,
      },
      {
        path: '/approval/announcement',
        name: '公告审批',
        component: './Approval/Announcement/index',
        hideInMenu: true,
      },
      {
        path: '/approval/announcement/details/:id',
        name: '详情',
        component: './Approval/Announcement/Details',
        hideInMenu: true,
      },
      {
        path: '/approval/pending-approval',
        name: '待审批',
        component: './Approval/PendingApproval/index',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/sale',
    name: '销售管理',
    icon: 'AccountBook',
    access: 'canReadSale',
    routes: [
      {
        path: '/sale',
        redirect: '/sale/sales-plan-table',
      },
      {
        path: '/sale/sales-plan-table',
        name: '销售计划表',
        component: './Sale/SalesPlanTable',
        access: 'canReadSalePlanTable',
      },
      {
        path: '/sale/collection-plan-table',
        name: '收款计划表',
        component: './Sale/CollectionPlanTable',
        access: 'canReadCollectionPlanTable',
      },
      {
        path: '/sale/payment-plan-table',
        name: '付款计划表',
        component: './Sale/PaymentPlanTable',
        access: 'canReadPaymentPlanTable',
      },
      {
        path: '/sale/performance-config',
        name: '项目核算表',
        component: './Sale/PerformanceConfig',
        access: 'canReadPerformanceConfig',
      },
      {
        path: '/sale/performance-result',
        name: '年度业绩考核表',
        component: './Sale/PerformanceResult',
        access: 'canReadPerformanceResult',
      },
      {
        path: '/sale/performance-result/:year',
        name: '年度业绩考核明细表',
        component: './Sale/PerformanceResult/components/ResultTable',
        hideInMenu: true,
        access: 'canReadPerformanceResult',
      },
      {
        path: '/sale/performance-config/edit/:id/:receiveYear',
        name: '编辑年度核算单',
        component: './Sale/PerformanceConfig/components/MainDetails',
        hideInMenu: true,
        access: 'canEditPerformanceConfig',
      },
      {
        path: '/sale/performance-config/initDetails/:id/:receiveYear',
        name: '年度核算单',
        component: './Sale/PerformanceConfig/components/MainDetails',
        hideInMenu: true,
        access: 'canReadPerformanceConfig',
      },
      {
        path: '/sale/performance-result/finalDetails/:id/:receiveYear',
        name: '年度核算单',
        component: './Sale/PerformanceConfig/components/MainDetails',
        hideInMenu: true,
        access: 'canReadPerformanceResult',
      },
      {
        path: '/sale/performance-result/printInfo/:receiveYear/:salePersonId',
        name: '年度业绩考核汇总表',
        component: './Sale/PerformanceResult/components/PrintInfo',
        hideInMenu: true,
        access: 'canPrintPerformanceResult',
      },
    ],
  },
  {
    path: '/profit',
    name: '利润管理',
    icon: 'lineChart',
    access: 'canReadProfit',
    routes: [
      {
        path: '/profit',
        redirect: '/profit/project-delivery-sh',
      },
      {
        path: '/profit/project-delivery-sh',
        component: './Profit/ProjectDelivery',
        access: 'canReadProfitProjectDelivery',
        name: '售后项目交付',
      },
      {
        path: '/profit/project-delivery-kf',
        component: './Profit/DevelopProjectDelivery',
        access: 'canReadProfitDevelopProjectDelivery',
        name: '开发项目交付',
      },
      {
        path: '/profit/main-contract-profit',
        component: './Profit/MainContractProfit',
        access: 'canReadProfitMainContract',
        name: '主合同利润报表',
      },
    ],
  },
  {
    path: '/setting',
    name: '系统设置',
    icon: 'setting',
    access: 'canSetting',
    routes: [
      {
        path: '/setting',
        redirect: '/setting/employee-type',
      },
      {
        path: '/setting/employee-type',
        name: '员工类别',
        component: './SystemSetting/EmployeeType',
      },
      {
        path: '/setting/department',
        name: '部门列表',
        component: './SystemSetting/Department',
      },
      {
        path: '/setting/grade',
        name: '福利列表',
        component: './SystemSetting/Grade',
      },
      {
        path: '/setting/position',
        name: '职位列表',
        component: './SystemSetting/Position',
      },
      {
        path: '/setting/leave-type',
        name: '休假列表',
        component: './SystemSetting/LeaveType',
      },
      {
        path: '/setting/holiday-list',
        name: '假期列表',
        component: './SystemSetting/HolidayList',
      },
      {
        path: '/setting/project-type',
        name: '项目类型',
        component: './SystemSetting/ProjectType',
      },
      {
        path: '/setting/certificate-type',
        name: '证书类别',
        component: './SystemSetting/CertificateType',
      },
      {
        path: '/setting/parameter-configuration',
        name: '参数配置',
        access: 'canReadParameterConfiguration',
        component: './SystemSetting/ParameterConfiguration',
      },
    ],
  },
  {
    path: '/permission',
    name: '权限管理',
    icon: 'key',
    access: 'isRoleAdministrator',
    routes: [
      {
        path: '/permission',
        component: './Permission',
      },
      {
        path: '/permission/add',
        name: '角色配置',
        component: './Permission/components/Details',
        hideInMenu: true,
      },
      {
        path: '/permission/details/:id',
        name: '角色配置',
        component: './Permission/components/Details',
        hideInMenu: true,
      },
      {
        path: '/permission/assign-user/:id',
        name: '分配成员',
        component: './Permission/components/AssignUser',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/report-analysis',
    name: '报表分析',
    icon: 'lineChart',
    access: 'canReadAnalysis',
    routes: [
      {
        path: '/report-analysis',
        redirect: '/report-analysis/master-contract',
      },
      {
        path: '/report-analysis/master-contract',
        component: './ReportAnalysis/MasterContract',
        access: 'canReadAnalysisMasterContract',
        name: '主合同统计分析',
      },
      {
        path: '/report-analysis/internal-project',
        component: './ReportAnalysis/InternalProject',
        access: 'canReadAnalysisInternalProject',
        name: '内部项目统计分析',
      },
      {
        path: '/report-analysis/sales-project',
        component: './ReportAnalysis/SalesProject',
        access: 'canReadAnalysisSalesProject',
        name: '销售项目统计分析',
      },
      {
        path: '/report-analysis/pre-sales-project',
        component: './ReportAnalysis/PreSalesProject',
        access: 'canReadAnalysisPreSalesProject',
        name: '售前项目统计分析',
      },
      {
        path: '/report-analysis/after-sales-project',
        component: './ReportAnalysis/AfterSalesProject',
        access: 'canReadAnalysisAfterSalesProject',
        name: '售后项目统计分析',
      },
      {
        path: '/report-analysis/sales-plan-summary-table',
        component: './ReportAnalysis/SalesPlanSummaryTable',
        access: 'canReadTableSalesPlan',
        name: '销售计划汇总表',
      },
      {
        path: '/report-analysis/collection-plan-summary-table',
        component: './ReportAnalysis/CollectionPlanSummaryTable',
        access: 'canReadTableCollectionPlan',
        name: '收款计划汇总表',
      },
      {
        path: '/report-analysis/procurement-payment-Plan-table',
        component: './ReportAnalysis/ProcurementPaymentPlanTable',
        access: 'canReadTableProcurementPaymentPlan',
        name: '采购付款计划汇总表',
      },
      {
        path: '/report-analysis/workload-statistics',
        component: './ReportAnalysis/WorkloadStatistics',
        access: 'canReadWorkloadStatistics',
        name: '工作量统计分析',
      },
    ],
  },
  {
    path: '/',
    redirect: '/personal-center',
  },

  {
    path: '*',
    layout: false,
    component: './404',
  },
];
