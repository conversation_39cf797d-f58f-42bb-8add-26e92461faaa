import { Settings as LayoutSettings } from '@ant-design/pro-components';

/**
 * @name layout默认配置
 */
const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  REQUEST_API: string; //请求地址
} = {
  navTheme: 'light',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  colorPrimary: '#13c2c2',
  title: 'OA系统',
  pwa: false,
  logo: '/images/logo.png',
  iconfontUrl: '',
  menu: {
    autoClose: false,
    locale: false,
    collapsedShowTitle: false,
    type: 'sub',
  },
  // REQUEST_API: 'http://192.168.0.32:8080',
  REQUEST_API: 'http://192.168.100.102:31999',
  // REQUEST_API: 'http://192.168.0.159:8080',
};

export default Settings;
